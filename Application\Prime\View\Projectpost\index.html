<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化岗位管理页面样式 */
                .projectpost-index-wrapper {
                    width: 100%;
                }

                /* 现代化页面标题 */
                .projectpost-index-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                    width: 100%;
                }

                .projectpost-index-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .projectpost-index-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .projectpost-index-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .projectpost-index-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .projectpost-index-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .projectpost-index-title-main {
                    font-size: 2.3rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .projectpost-index-title-sub {
                    font-size: 1.7rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .projectpost-index-actions {
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                }

                .projectpost-index-search-toggle,
                .projectpost-index-add-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .projectpost-index-search-toggle {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .projectpost-index-search-toggle:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                .projectpost-index-add-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .projectpost-index-add-btn:hover {
                    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                /* 隐藏式搜索面板 */
                .projectpost-index-search-panel {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    max-height: 0;
                    opacity: 0;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .projectpost-index-search-panel.show {
                    max-height: 500px;
                    opacity: 1;
                }

                .projectpost-index-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .projectpost-index-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .projectpost-index-search-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .projectpost-index-search-body {
                    padding: 2rem;
                    background: white;
                }

                .projectpost-index-search-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .projectpost-index-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .projectpost-index-form-label {
                    font-size: 1.7rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .projectpost-index-form-input,
                .projectpost-index-form-select {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .projectpost-index-form-input:focus,
                .projectpost-index-form-select:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .projectpost-index-form-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .projectpost-index-search-actions {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .projectpost-index-search-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .projectpost-index-search-btn.btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .projectpost-index-search-btn.btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                    text-decoration: none;
                }

                .projectpost-index-search-btn.btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .projectpost-index-search-btn.btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    transform: translateY(-1px);
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                    text-decoration: none;
                }

                /* 现代化导航标签 */
                .projectpost-index-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .projectpost-index-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .projectpost-index-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .projectpost-index-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .projectpost-index-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .projectpost-index-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .projectpost-index-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .projectpost-index-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化列表容器 */
                .projectpost-index-list-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    overflow: hidden;
                }

                .projectpost-list-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                }

                .projectpost-list-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .projectpost-list-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0;
                }

                .projectpost-list-body {
                    background: white;
                }

                /* 列表项样式 */
                .projectpost-list-item {
                    display: flex;
                    align-items: flex-start;
                    padding: 2rem;
                    border-bottom: 1px solid #f1f5f9;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    gap: 2rem;
                }

                .projectpost-list-item:last-child {
                    border-bottom: none;
                }

                .projectpost-list-item:hover {
                    background: #f8fafc;
                    transform: translateX(4px);
                }

                .projectpost-list-item::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    width: 4px;
                    background: transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .projectpost-list-item:hover::before {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                }

                /* ID和置顶区域 */
                .projectpost-item-id-section {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 0.5rem;
                    min-width: 6rem;
                }

                .projectpost-item-id {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.5rem;
                    font-weight: 700;
                    text-align: center;
                    min-width: 5rem;
                }

                .projectpost-top-badge {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                    padding: 0.25rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                /* 内容区域 */
                .projectpost-item-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .projectpost-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 1rem;
                    flex-wrap: wrap;
                }

                .projectpost-item-title {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .projectpost-item-status {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .projectpost-status-badge {
                    padding: 0.5rem 1rem;
                    border-radius: 2rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    display: flex;
                    align-items: center;
                    gap: 0.25rem;
                }

                .projectpost-status-badge.label-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .projectpost-status-badge.label-warning {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: white;
                }

                .projectpost-status-badge.label-danger {
                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                    color: white;
                }

                .projectpost-status-badge.label-info {
                    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
                    color: white;
                }

                /* 基本信息样式 */
                .projectpost-basic-info {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .projectpost-info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1rem;
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.5rem;
                    font-size: 1.5rem;
                    min-width: 200px;
                    flex: 1;
                }

                .projectpost-info-item i {
                    color: #667eea;
                    font-size: 1.25rem;
                    width: 1.5rem;
                }

                .projectpost-info-label {
                    color: #6b7280;
                    font-weight: 500;
                }

                .projectpost-info-value {
                    color: #374151;
                    font-weight: 600;
                }

                /* 分组样式 */
                .projectpost-requirements,
                .projectpost-pricing,
                .projectpost-settlement {
                    background: #f8fafc;
                    border: 1px solid #e2e8f0;
                    border-radius: 0.75rem;
                    padding: 1.5rem;
                }

                .projectpost-section-title {
                    font-size: 1.75rem;
                    font-weight: 600;
                    color: #1a202c;
                    margin: 0 0 1rem 0;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding-bottom: 0.75rem;
                    border-bottom: 2px solid #667eea;
                }

                .projectpost-section-title i {
                    color: #667eea;
                    font-size: 1.5rem;
                }

                /* 人员要求网格 */
                .projectpost-req-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                }

                .projectpost-req-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 0.75rem;
                    background: white;
                    border-radius: 0.5rem;
                    border: 1px solid #e5e7eb;
                    font-size: 1.5rem;
                }

                .projectpost-req-item i {
                    width: 1.5rem;
                    color: #667eea;
                    font-size: 1.25rem;
                }

                .projectpost-req-label {
                    color: #6b7280;
                    font-weight: 500;
                }

                .projectpost-req-value {
                    color: #374151;
                    font-weight: 600;
                    flex: 1;
                }

                .projectpost-tags .projectpost-req-value {
                    color: #667eea;
                }

                /* 报价信息网格 */
                .projectpost-price-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 1rem;
                }

                .projectpost-price-item {
                    background: white;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.5rem;
                    padding: 1rem;
                    text-align: center;
                }

                .projectpost-price-label {
                    display: block;
                    font-size: 1.25rem;
                    color: #6b7280;
                    font-weight: 500;
                    margin-bottom: 0.5rem;
                }

                .projectpost-price-value {
                    display: block;
                    font-size: 1.75rem;
                    font-weight: 700;
                }

                .projectpost-cost {
                    color: #ef4444;
                }

                .projectpost-service {
                    color: #3b82f6;
                }

                .projectpost-max {
                    color: #8b5cf6;
                }

                .projectpost-reward {
                    color: #10b981;
                }

                .projectpost-free-project {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 2rem;
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                    border-radius: 0.75rem;
                    font-size: 1.75rem;
                    font-weight: 700;
                }

                /* 结算信息 */
                .projectpost-settle-item {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    padding: 1rem;
                    background: white;
                    border-radius: 0.5rem;
                    border: 1px solid #e5e7eb;
                    font-size: 1.5rem;
                }

                .projectpost-settle-item i {
                    color: #667eea;
                    font-size: 1.25rem;
                }

                .projectpost-settle-label {
                    color: #6b7280;
                    font-weight: 500;
                }

                .projectpost-settle-value {
                    color: #374151;
                    font-weight: 600;
                }

                /* 操作按钮区域 */
                .projectpost-item-actions {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    align-items: flex-end;
                    min-width: 120px;
                }

                .projectpost-item-actions .btn {
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    padding: 0.5rem 1rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    text-align: center;
                    min-width: 80px;
                }

                .projectpost-item-actions .btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    text-decoration: none;
                }

                /* 动画效果 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .projectpost-index-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .projectpost-index-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .projectpost-index-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .projectpost-index-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .projectpost-index-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .projectpost-req-grid,
                    .projectpost-price-grid {
                        grid-template-columns: 1fr;
                    }

                    .projectpost-basic-info {
                        flex-direction: column;
                    }
                }

                @media (max-width: 768px) {
                    .projectpost-list-item {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 1rem;
                        padding: 1.5rem 1rem;
                    }

                    .projectpost-item-content {
                        margin-left: 0;
                        width: 100%;
                    }

                    .projectpost-item-actions {
                        margin-left: 0;
                        width: 100%;
                        flex-direction: row;
                        justify-content: flex-start;
                        align-items: center;
                    }

                    .projectpost-index-nav-tabs {
                        flex-direction: column;
                    }

                    .projectpost-index-nav-item {
                        flex: none;
                    }

                    .projectpost-index-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .projectpost-index-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .projectpost-index-title-main {
                        font-size: 2rem;
                    }

                    .projectpost-index-title-sub {
                        font-size: 1.45rem;
                    }

                    .projectpost-index-actions {
                        flex-direction: column;
                        width: 100%;
                    }

                    .projectpost-index-search-toggle,
                    .projectpost-index-add-btn {
                        width: 100%;
                        justify-content: center;
                    }
                }

            </style>

            <!-- 现代化页面标题 -->
            <div class="projectpost-index-header projectpost-index-fade-in">
                <div class="projectpost-index-header-content">
                    <div class="projectpost-index-title">
                        <div class="projectpost-index-title-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="projectpost-index-title-text">
                            <h1 class="projectpost-index-title-main">岗位管理</h1>
                            <p class="projectpost-index-title-sub">Project Post Management</p>
                        </div>
                    </div>
                    <div class="projectpost-index-actions">
                        <button type="button" class="projectpost-index-search-toggle" onclick="toggleSearchPanel()">
                            <i class="fa fa-search"></i>
                            <span>搜索筛选</span>
                        </button>
                        <a href="{:U('Projectpost/edit')}" class="projectpost-index-add-btn">
                            <i class="fa fa-plus"></i>
                            <span>岗位添加</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 现代化搜索面板 -->
            <div class="projectpost-index-search-panel projectpost-index-fade-in-delay-1" id="searchPanel">
                <div class="projectpost-index-search-header">
                    <div class="projectpost-index-search-icon">
                        <i class="fa fa-search"></i>
                    </div>
                    <h3 class="projectpost-index-search-title">搜索筛选</h3>
                </div>
                <div class="projectpost-index-search-body">
                    <form method="get" class="projectpost-index-search-form" role="form">
                        <div class="projectpost-index-form-group">
                            <label class="projectpost-index-form-label">搜索字段</label>
                            <select class="projectpost-index-form-select" name="kw">
                                <php>foreach($c_kw as $key=>$value){</php>
                                <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                <php>}</php>
                            </select>
                        </div>
                        <div class="projectpost-index-form-group">
                            <label class="projectpost-index-form-label">搜索内容</label>
                            <input class="projectpost-index-form-input" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容..." />
                        </div>
                        <div class="projectpost-index-form-group">
                            <label class="projectpost-index-form-label">岗位状态</label>
                            <select name="status" class="projectpost-index-form-select">
                                <option value="">全部状态</option>
                                <php>foreach($statusList as $key => $status) {</php>
                                <option value="{$key}" {:$_get['status'] != '' && $_get['status'] == $key ? "selected" : ''}>{$status.text}</option>
                                <php>} </php>
                            </select>
                        </div>
                        <div class="projectpost-index-search-actions">
                            <button type="submit" class="projectpost-index-search-btn btn-primary">
                                <i class="fa fa-search"></i>
                                <span>搜索</span>
                            </button>
                            <a href="{:U('Projectpost/index')}" class="projectpost-index-search-btn btn-secondary">
                                <i class="fa fa-refresh"></i>
                                <span>重置</span>
                            </a>
                        </div>
                    </form>
                </div>
            </div>


            <!-- 现代化导航标签 -->
            <div class="projectpost-index-nav-container projectpost-index-fade-in-delay-2">
                <ul class="projectpost-index-nav-tabs">
                    <li class="projectpost-index-nav-item">
                        <a href="{:U('Project/index')}" class="projectpost-index-nav-link">
                            <i class="fa fa-briefcase projectpost-index-nav-icon"></i>
                            <span>项目管理</span>
                        </a>
                    </li>
                    <li class="projectpost-index-nav-item">
                        <a href="{:U('Project/edit')}" class="projectpost-index-nav-link">
                            <i class="fa fa-plus projectpost-index-nav-icon"></i>
                            <span>添加项目</span>
                        </a>
                    </li>
                    <li class="projectpost-index-nav-item">
                        <a href="{:U('Projectpost/index')}" class="projectpost-index-nav-link active">
                            <i class="fa fa-users projectpost-index-nav-icon"></i>
                            <span>岗位管理</span>
                        </a>
                    </li>
                    <li class="projectpost-index-nav-item">
                        <a href="{:U('Projectpost/edit')}" class="projectpost-index-nav-link">
                            <i class="fa fa-plus projectpost-index-nav-icon"></i>
                            <span>添加岗位</span>
                        </a>
                    </li>
                    <li class="projectpost-index-nav-item">
                        <a href="{:U('Project/quotation')}" class="projectpost-index-nav-link">
                            <i class="fa fa-calculator projectpost-index-nav-icon"></i>
                            <span>报价管理</span>
                        </a>
                    </li>
                </ul>
            </div>


            <!-- 现代化列表容器 -->
            <form action="" method="post">
                <div class="projectpost-index-list-container projectpost-index-fade-in-delay-3">
                    <div class="projectpost-list-header">
                        <div class="projectpost-list-icon">
                            <i class="fa fa-users"></i>
                        </div>
                        <h3 class="projectpost-list-title">岗位列表</h3>
                    </div>
                    <div class="projectpost-list-body">
                        <php>foreach($list as $v) { </php>
                        <div class="projectpost-list-item">
                            <!-- ID显示和置顶标识 -->
                            <div class="projectpost-item-id-section">
                                <div class="projectpost-item-id">
                                    #{$v.id}
                                </div>
                                <php>if($v['is_top']) {</php>
                                <div class="projectpost-top-badge">
                                    <i class="fa fa-star"></i>
                                    <span>置顶</span>
                                </div>
                                <php>}</php>
                            </div>

                            <!-- 内容区域 -->
                            <div class="projectpost-item-content">
                                <!-- 岗位名称和状态 -->
                                <div class="projectpost-item-header">
                                    <h4 class="projectpost-item-title">
                                        {$v.job_name}
                                    </h4>
                                    <div class="projectpost-item-status">
                                        <span class="projectpost-status-badge label-{:$statusList[$v['status']]['style']}">
                                            <i class="fa fa-circle"></i>
                                            <span>{:$statusList[$v['status']]['text']}</span>
                                        </span>
                                    </div>
                                </div>

                                <!-- 基本信息 -->
                                <div class="projectpost-basic-info">
                                    <div class="projectpost-info-item">
                                        <i class="fa fa-briefcase"></i>
                                        <span class="projectpost-info-label">项目名称：</span>
                                        <span class="projectpost-info-value">{:$projectList[$v['project_id']]['name']}</span>
                                    </div>
                                    <div class="projectpost-info-item">
                                        <i class="fa fa-folder-open"></i>
                                        <span class="projectpost-info-label">项目类别：</span>
                                        <span class="projectpost-info-value">{:$categoryList[$projectList[$v['project_id']]['category']]}</span>
                                    </div>
                                    <php>if($v['channel_id'] && isset($channelList[$v['channel_id']])) {</php>
                                    <div class="projectpost-info-item">
                                        <i class="fa fa-bullhorn"></i>
                                        <span class="projectpost-info-label">招聘渠道：</span>
                                        <span class="projectpost-info-value">{:$channelList[$v['channel_id']]}</span>
                                    </div>
                                    <php>}</php>
                                </div>

                                <!-- 人员要求 -->
                                <div class="projectpost-requirements">
                                    <h5 class="projectpost-section-title">
                                        <i class="fa fa-users"></i>
                                        人员要求
                                    </h5>
                                    
                                    <div class="projectpost-req-grid">
                                        <div class="projectpost-req-item">
                                            <i class="fa fa-graduation-cap"></i>
                                            <span class="projectpost-req-label">学历：</span>
                                            <span class="projectpost-req-value">{:$qualificationList[$v['qualification']]['text']}</span>
                                        </div>
                                        <div class="projectpost-req-item">
                                            <i class="fa fa-book"></i>
                                            <span class="projectpost-req-label">专业：</span>
                                            <span class="projectpost-req-value">{:$v['major'] ?: '不限'}</span>
                                        </div>
                                        <div class="projectpost-req-item">
                                            <i class="fa fa-calendar"></i>
                                            <span class="projectpost-req-label">年龄：</span>
                                            <span class="projectpost-req-value">{:$v['min_age'] ? : '--'}-{:$v['max_age'] ? : '--'}岁</span>
                                        </div>
                                        <div class="projectpost-req-item">
                                            <i class="fa fa-venus-mars"></i>
                                            <span class="projectpost-req-label">性别：</span>
                                            <span class="projectpost-req-value">{:$sexList[$v['sex']]['text']}</span>
                                        </div>
                                        
                                    </div>
                                    <php>if($v['tag']) {</php>
                                        <div class="projectpost-req-item projectpost-tags" style="margin-top: 8px;">
                                            <i class="fa fa-tags"></i>
                                            <span class="projectpost-req-label">标签：</span>
                                            <span class="projectpost-req-value">{$v.tag}</span>
                                        </div>
                                        <php>}</php>
                                </div>

                                <!-- 报价信息 -->
                                <div class="projectpost-pricing">
                                    <h5 class="projectpost-section-title">
                                        <i class="fa fa-money"></i>
                                        报价信息
                                    </h5>
                                    <php>if(!$projectList[$v['project_id']]['is_free']) { </php>
                                    <div class="projectpost-price-grid">
                                        <div class="projectpost-price-item">
                                            <span class="projectpost-price-label">内部成本：</span>
                                            <span class="projectpost-price-value projectpost-cost">￥{$v.internal_costs}</span>
                                        </div>
                                        <div class="projectpost-price-item">
                                            <span class="projectpost-price-label">{$v.service_price_text}收费：</span>
                                            <span class="projectpost-price-value projectpost-service">￥{$v.service_price}</span>
                                        </div>
                                        <div class="projectpost-price-item">
                                            <span class="projectpost-price-label">{$v.max_price_text}收费：</span>
                                            <span class="projectpost-price-value projectpost-max">￥{$v.max_price}</span>
                                        </div>
                                        <div class="projectpost-price-item">
                                            <span class="projectpost-price-label">服务奖励：</span>
                                            <span class="projectpost-price-value projectpost-reward">￥{$v.reward}</span>
                                        </div>
                                    </div>
                                    <php>} else {</php>
                                    <div class="projectpost-free-project">
                                        <i class="fa fa-heart"></i>
                                        <span>免费公益项目</span>
                                    </div>
                                    <php>}</php>
                                </div>


                                <!-- 结算信息 -->
                                <div class="projectpost-settlement">
                                    <h5 class="projectpost-section-title">
                                        <i class="fa fa-calendar-check-o"></i>
                                        结算信息
                                    </h5>
                                    <div class="projectpost-settle-item">
                                        <i class="fa fa-clock-o"></i>
                                        <span class="projectpost-settle-label">结算周期：</span>
                                        <span class="projectpost-settle-value">{$v.settle_day}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="projectpost-item-actions">
                                <php>echo projectpostStatBtn($v['id'], $v['status'], $v['is_top']);</php>
                            </div>
                        </div>
                        <php>}</php>
                    </div>
                </div>

                <!-- 分页信息 -->
                <div class="projectpost-pagination-container" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 2rem; text-align: center; margin-top: 2rem;">
                    {$page}
                </div>
            </form>
        </div>
    </div>
</div>
<include file="block/footer" />
<script>
    $(document).ready(function() {
        // 列表项悬停效果增强
        $('.projectpost-list-item').hover(
            function() {
                $(this).addClass('hover-effect');
            },
            function() {
                $(this).removeClass('hover-effect');
            }
        );

        // 岗位状态按钮点击事件
        $('.js_status').click(function() {
            var that = $(this),
                url = that.attr('url');

            // 添加加载状态
            var originalText = that.html();
            that.html('<i class="fa fa-spinner fa-spin"></i> 处理中...');
            that.prop('disabled', true);

            $.get(url, function(data) {
                window.location.reload();
            }).fail(function() {
                that.html(originalText);
                that.prop('disabled', false);
                layer.msg('操作失败，请重试', {icon: 2});
            });
        });

        // 空状态处理
        var totalItems = $('.projectpost-list-item').length;
        if (totalItems === 0) {
            var $emptyState = $('<div class="empty-state" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1); padding: 4rem 2rem; text-align: center; margin: 2rem 0;"><div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 1.5rem;"><i class="fa fa-users"></i></div><h3 style="font-size: 1.75rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">暂无岗位数据</h3><p style="font-size: 1.5rem; color: #6b7280; margin-bottom: 2rem;">请尝试调整搜索条件或添加新的岗位。</p><a href="{:U(\'Projectpost/edit\')}" class="btn btn-primary" style="padding: 0.75rem 1.5rem; font-size: 1.5rem;"><i class="fa fa-plus"></i> 添加岗位</a></div>');
            $('.projectpost-index-list-container .projectpost-list-body').append($emptyState);
        }
    });

    // 搜索面板切换功能
    function toggleSearchPanel() {
        var panel = document.getElementById('searchPanel');
        if (panel.classList.contains('show')) {
            panel.classList.remove('show');
        } else {
            panel.classList.add('show');
        }
    }
</script>