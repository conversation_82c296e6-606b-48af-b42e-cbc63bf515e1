<style>
/* 招聘公告系统统一样式 - 基于简历管理风格 */

/* 全局字体大小调整 */
.recruitment-page-wrapper,
.recruitment-page-wrapper * {
    font-size: inherit;
}

.recruitment-page-wrapper {
    font-size: 16px; /* 基础字体大小从默认的14px增加到16px */
}

/* 现代化页面容器 */
.recruitment-page-wrapper {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 0;
    margin: 0;
}

.recruitment-page-container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 1.5rem 1rem;
    background: transparent;
}

/* 现代化页面标题 */
.recruitment-page-header {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
    position: relative;
}

.recruitment-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.recruitment-header-content {
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.recruitment-page-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0;
}

.recruitment-title-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.recruitment-title-text {
    display: flex;
    flex-direction: column;
}

.recruitment-title-main {
    font-size: 2.8rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
    line-height: 1.2;
}

.recruitment-title-sub {
    font-size: 1.4rem;
    color: #718096;
    margin: 0;
    font-weight: 400;
}

/* 页面头部操作按钮 */
.recruitment-header-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.recruitment-header-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
}

.recruitment-header-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.recruitment-header-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.recruitment-header-btn-primary:hover {
    color: white;
}

.recruitment-header-btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.recruitment-header-btn-success:hover {
    color: white;
}

/* 现代化搜索面板 */
.recruitment-search-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
}

.recruitment-search-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.recruitment-search-body {
    padding: 2rem;
}

.form-inline .form-group {
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    padding: 0.875rem 1.25rem;
    transition: all 0.3s ease;
    font-size: 1.25rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

/* 按钮样式统一 */
.btn {
    border-radius: 0.5rem;
    padding: 0.875rem 1.75rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    font-size: 1.3rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    text-decoration: none;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.btn-warning:hover {
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
}

.btn-info:hover {
    color: white;
}

.btn-default {
    background: #f8fafc;
    color: #374151;
    border: 1px solid #e2e8f0;
}

.btn-default:hover {
    background: #f1f5f9;
    color: #374151;
}

.btn-sm {
    padding: 0.625rem 1.25rem;
    font-size: 1.15rem;
}

/* 现代化表格容器 */
.recruitment-table-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
}

.table-responsive {
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

.table {
    margin-bottom: 0;
    background: white;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #374151;
    border: none;
    padding: 1.5rem 1rem;
    font-weight: 600;
    text-align: left;
    font-size: 1.3rem;
    border-bottom: 2px solid #e2e8f0;
}

.table tbody td {
    padding: 1.25rem 1rem;
    vertical-align: middle;
    border-top: 1px solid #f1f5f9;
    font-size: 1.3rem;
}

.table-hover tbody tr:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
}

/* 现代化标签样式 */
.label, .badge {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 1.15rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.label-success, .badge-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.label-warning, .badge-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
}

.label-danger, .badge-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.label-info, .badge-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
}

/* 统计卡片样式 */
.recruitment-stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.recruitment-stat-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.recruitment-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.recruitment-stat-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem auto;
}

.recruitment-stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1a202c;
    margin: 0;
    line-height: 1;
}

.recruitment-stat-label {
    font-size: 1rem;
    color: #718096;
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

/* 现代化分页容器 */
.recruitment-pagination-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    padding: 1.5rem 2rem;
    margin-top: 2rem;
}

.dataTables_info {
    color: #374151;
    font-size: 1.25rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dataTables_info strong {
    color: #667eea;
    font-weight: 700;
}

.pagination-jump {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-jump .input-group {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination-jump .input-group-addon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-size: 1rem;
    font-weight: 600;
    padding: 0.875rem 1.25rem;
}

.pagination-jump .form-control {
    border: none;
    font-size: 1rem;
    text-align: center;
    padding: 0.875rem;
}

.pagination-jump .btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.875rem 1.25rem;
}

.pagination-jump .btn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.pagination {
    margin: 0;
    display: flex;
    gap: 0.25rem;
}

.pagination > li > a,
.pagination > li > span {
    color: #374151;
    border: 1px solid #e2e8f0;
    padding: 0.875rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    background: white;
    font-size: 1rem;
}

.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    box-shadow: 0 4px 6px rgba(102, 126, 234, 0.3);
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
    color: #9ca3af;
    cursor: not-allowed;
    background-color: #f9fafb;
    border-color: #e5e7eb;
    transform: none;
    box-shadow: none;
}

/* 模态框样式 */
.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem 2rem;
}

.modal-header .close {
    color: white;
    opacity: 0.8;
    font-size: 1.5rem;
    font-weight: 300;
}

.modal-header .close:hover {
    opacity: 1;
}

.modal-body {
    padding: 2rem;
    background-color: #f8fafc;
}

.modal-dialog {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal-content {
    border: none;
    border-radius: 1rem;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .recruitment-page-container {
        padding: 1.5rem;
    }

    .recruitment-header-content {
        padding: 1.5rem;
    }

    .recruitment-search-header {
        padding: 1.25rem 1.5rem;
    }

    .recruitment-search-body {
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .recruitment-page-container {
        padding: 1rem 0.5rem;
    }

    .recruitment-header-content {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .recruitment-page-title {
        justify-content: center;
    }

    .recruitment-header-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.75rem;
    }

    .recruitment-header-btn {
        width: 100%;
        justify-content: center;
    }

    .form-inline .form-group {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
        margin-right: 0;
    }

    .form-inline .form-control {
        width: 100%;
    }

    .table-responsive {
        font-size: 1rem;
    }

    .table thead th,
    .table tbody td {
        padding: 1rem 0.75rem;
    }

    .btn-sm {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }

    .recruitment-stats-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .recruitment-stat-card {
        padding: 1.5rem;
    }

    .recruitment-pagination-container {
        padding: 1rem;
    }

    .pagination > li > a,
    .pagination > li > span {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .recruitment-page-container {
        padding: 0.75rem;
    }

    .recruitment-header-content {
        padding: 1rem;
    }

    .recruitment-title-main {
        font-size: 2.2rem;
    }

    .recruitment-title-sub {
        font-size: 1.25rem;
    }

    .recruitment-search-header {
        padding: 1rem;
    }

    .recruitment-search-body {
        padding: 1rem;
    }

    .recruitment-stat-card {
        padding: 1rem;
    }

    .recruitment-stat-number {
        font-size: 2rem;
    }

    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 1.15rem;
    }

    .pagination > li > a,
    .pagination > li > span {
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
    }

    .pagination-jump .input-group {
        width: 120px;
    }
}
</style>
