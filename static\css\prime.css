/* 自定义 */
.table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{white-space:inherit;word-break: break-all;overflow:visible; text-overflow:ellipsis;}
.table-responsive .panel-body {overflow:auto}
.table{min-width:550px}
.btnc{padding:4px 8px; margin-bottom:1px;}
.alert{padding:10px; }

/* 简历页面优化样式 */
.resume-card .btn {
    margin: 2px;
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
}

.resume-card .btn-primary {
    background: #3498db;
    border-color: #3498db;
}

.resume-card .btn-success {
    background: #2ecc71;
    border-color: #2ecc71;
}

.resume-card .btn-warning {
    background: #f39c12;
    border-color: #f39c12;
}

.resume-card .btn-danger {
    background: #e74c3c;
    border-color: #e74c3c;
}

/* 分页样式优化 */
.pagination {
    margin: 20px 0;
}

.pagination > li > a,
.pagination > li > span {
    border-radius: 4px;
    margin: 0 2px;
}

/* 响应式优化 */
@media (max-width: 480px) {
    .search-form .form-group {
        width: 100%;
        margin-right: 0;
    }

    .search-form .form-control {
        width: 100%;
    }

    .search-form .btn {
        width: 100%;
        margin-top: 10px;
    }
}

/* 全局链接样式优化 */
a {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

a:hover {
    color: #5a67d8;
    text-decoration: none;
}

a:focus {
    outline: none;
    color: #5a67d8;
    text-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    border-radius: 2px;
}

a:active {
    color: #4c51bf;
    transform: translateY(1px);
    transition: all 0.1s ease;
}

/* 按钮链接样式 */
a.btn {
    transform: none;
    text-shadow: none;
}

a.btn:active {
    transform: translateY(1px);
}

/* 表格中的链接 */
.table a {
    font-weight: 500;
    padding: 2px 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.table a:hover {
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 6px;
}

/* 面包屑链接 */
.breadcrumb a {
    color: #6c757d;
}

.breadcrumb a:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    margin: -4px -4px;
}

/* 导航链接 */
.nav a {
    border-radius: 6px;
    transition: all 0.3s ease;
}

.nav a:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(3px);
}

/* 卡片中的链接 */
.card a, .panel a {
    font-weight: 500;
}

.card a:hover, .panel a:hover {
    background: rgba(102, 126, 234, 0.08);
    padding: 2px 6px;
    border-radius: 4px;
    margin: -2px -2px;
}

/* 下拉菜单链接 */
.dropdown-menu a {
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 1px 4px;
}

.dropdown-menu a:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%);
    color: #667eea;
    transform: translateX(3px);
}

/* 禁用链接样式 */
a.disabled, a[disabled] {
    color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

a.disabled:hover, a[disabled]:hover {
    color: #6c757d;
    text-decoration: none;
    transform: none;
    background: none;
}