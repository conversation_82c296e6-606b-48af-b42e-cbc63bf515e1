<?php
namespace Common\Model;

use Think\Model;

class PostRequirementsModel extends Model
{
    protected $tableName = 'post_requirements';
    
    protected $_auto = [
        ['create_time', 'time', 1, 'function'],
        ['update_time', 'time', 2, 'function'],
    ];

    // 性别选项 - 与前端保持一致：1=不限，2=男，3=女
    public $genderOptions = [
        '1' => '不限',
        '2' => '男',
        '3' => '女'
    ];

    // 学历选项
    public $educationOptions = [
        '0' => '不限',
        '1' => '中专及以上',
        '2' => '大专及以上',
        '3' => '本科及以上',
        '4' => '研究生及以上',
        '5' => '硕士及以上'
    ];

    // 恐高选项
    public $afraidHeightsOptions = [
        '0' => '不限',
        '1' => '不能恐高',
        '2' => '可以恐高'
    ];

    // 婚姻状况选项
    public $maritalStatusOptions = [
        '0' => '不限',
        '1' => '未婚',
        '2' => '已婚'
    ];

    // 应届生选项
    public $freshGraduateOptions = [
        '0' => '不限',
        '1' => '仅限应届生',
        '2' => '非应届生'
    ];

    /**
     * 获取岗位要求
     */
    public function getRequirements($postId, $noticeId)
    {
        return $this->where([
            'post_id' => $postId,
            'notice_id' => $noticeId
        ])->find();
    }

    /**
     * 保存岗位要求
     */
    public function saveRequirements($data)
    {
        // 设置时间戳
        $data['update_time'] = time();

        $existing = $this->where([
            'post_id' => $data['post_id'],
            'notice_id' => $data['notice_id']
        ])->find();

        if ($existing) {
            // 更新现有记录
            $data['id'] = $existing['id'];
            $result = $this->save($data);
            // save()方法成功时返回受影响行数(>=0)，失败时返回false
            return $result !== false ? $existing['id'] : false;
        } else {
            // 创建新记录
            $data['create_time'] = time();
            $result = $this->add($data);
            return $result;
        }
    }

    /**
     * 批量设置岗位要求
     */
    public function batchSaveRequirements($noticeId, $postIds, $requirements)
    {
        $this->startTrans();
        try {
            foreach ($postIds as $postId) {
                $data = $requirements;
                $data['post_id'] = $postId;
                $data['notice_id'] = $noticeId;
                $this->saveRequirements($data);
            }
            $this->commit();
            return true;
        } catch (Exception $e) {
            $this->rollback();
            return false;
        }
    }

    /**
     * 根据简历数据检查是否符合要求
     */
    public function checkResumeMatch($userJobData, $requirements)
    {
        $matchDetails = [];
        $score = 0;
        $totalChecks = 0;
        $passedChecks = 0;

        // 年龄检查
        if ($requirements['min_age'] || $requirements['max_age']) {
            $totalChecks++;
            $age = $this->calculateAge($userJobData['birthdate']);
            $ageMatch = true;
            
            if ($requirements['min_age'] && $age < $requirements['min_age']) {
                $ageMatch = false;
            }
            if ($requirements['max_age'] && $age > $requirements['max_age']) {
                $ageMatch = false;
            }
            
            if ($ageMatch) {
                $passedChecks++;
                $matchDetails['age'] = ['status' => 'pass', 'message' => "年龄{$age}岁符合要求"];
            } else {
                $matchDetails['age'] = ['status' => 'fail', 'message' => "年龄{$age}岁不符合要求"];
            }
        }

        // 性别检查 - 前端传递：1=不限，2=男，3=女
        if ($requirements['gender'] > 1) {
            $totalChecks++;
            $genderMatch = false;

            // 处理前端传递的gender值：1=不限，2=男，3=女
            // 转换为后端期望的值：0=不限，1=男，2=女
            $requiredGender = $requirements['gender'];
            if ($requiredGender == 2) {
                // 前端传递2表示男，对应后端的1
                $requiredGender = 1;
            } elseif ($requiredGender == 3) {
                // 前端传递3表示女，对应后端的2
                $requiredGender = 2;
            }

            if ($requiredGender == 1 && $userJobData['gender'] == '男') {
                $genderMatch = true;
            } elseif ($requiredGender == 2 && $userJobData['gender'] == '女') {
                $genderMatch = true;
            }

            if ($genderMatch) {
                $passedChecks++;
                $matchDetails['gender'] = ['status' => 'pass', 'message' => '性别符合要求'];
            } else {
                $matchDetails['gender'] = ['status' => 'fail', 'message' => '性别不符合要求'];
            }
        }

        // 身高检查
        if ($requirements['min_height'] || $requirements['max_height']) {
            $totalChecks++;
            $heightMatch = true;
            $height = intval($userJobData['height']);
            
            if ($requirements['min_height'] && $height < $requirements['min_height']) {
                $heightMatch = false;
            }
            if ($requirements['max_height'] && $height > $requirements['max_height']) {
                $heightMatch = false;
            }
            
            if ($heightMatch) {
                $passedChecks++;
                $matchDetails['height'] = ['status' => 'pass', 'message' => "身高{$height}cm符合要求"];
            } else {
                $matchDetails['height'] = ['status' => 'fail', 'message' => "身高{$height}cm不符合要求"];
            }
        }

        // 学历检查
        if ($requirements['education_level'] > 0) {
            $totalChecks++;
            $educationMatch = $this->checkEducationLevel($userJobData['education_level'], $requirements['education_level']);
            
            if ($educationMatch) {
                $passedChecks++;
                $matchDetails['education'] = ['status' => 'pass', 'message' => '学历符合要求'];
            } else {
                $matchDetails['education'] = ['status' => 'fail', 'message' => '学历不符合要求'];
            }
        }

        // 专业检查
        if (!empty($requirements['major_keywords'])) {
            $totalChecks++;
            $majorMatch = $this->checkMajorMatch($userJobData['major'], $requirements['major_keywords']);
            
            if ($majorMatch) {
                $passedChecks++;
                $matchDetails['major'] = ['status' => 'pass', 'message' => '专业符合要求'];
            } else {
                $matchDetails['major'] = ['status' => 'fail', 'message' => '专业不符合要求'];
            }
        }

        // 毕业年限检查
        if (isset($requirements['graduation_years']) && $requirements['graduation_years'] > 0) {
            $totalChecks++;
            $graduationYearsMatch = $this->checkGraduationYears($userJobData, $requirements['graduation_years']);

            if ($graduationYearsMatch) {
                $passedChecks++;
                $matchDetails['graduation_years'] = ['status' => 'pass', 'message' => "毕业年限符合要求"];
            } else {
                $matchDetails['graduation_years'] = ['status' => 'fail', 'message' => "毕业年限不符合要求"];
            }
        }

        // 应届生检查
        if (isset($requirements['is_fresh_graduate']) && $requirements['is_fresh_graduate'] > 0) {
            $totalChecks++;
            $isFreshGraduate = $this->checkIsFreshGraduate($userJobData);
            $freshGraduateMatch = false;

            if ($requirements['is_fresh_graduate'] == 1 && $isFreshGraduate) {
                // 仅限应届生
                $freshGraduateMatch = true;
            } elseif ($requirements['is_fresh_graduate'] == 2 && !$isFreshGraduate) {
                // 非应届生
                $freshGraduateMatch = true;
            }

            if ($freshGraduateMatch) {
                $passedChecks++;
                $matchDetails['fresh_graduate'] = ['status' => 'pass', 'message' => $isFreshGraduate ? '应届生身份符合要求' : '非应届生身份符合要求'];
            } else {
                $matchDetails['fresh_graduate'] = ['status' => 'fail', 'message' => $isFreshGraduate ? '要求非应届生，但您是应届生' : '要求应届生，但您不是应届生'];
            }
        }

        // 工作经验检查（保留用于应届生判断逻辑）
        if ($requirements['min_work_years'] || $requirements['max_work_years']) {
            $totalChecks++;
            $workYears = intval($userJobData['work_experience_years']);
            $workMatch = true;

            if ($requirements['min_work_years'] && $workYears < $requirements['min_work_years']) {
                $workMatch = false;
            }
            if ($requirements['max_work_years'] && $workYears > $requirements['max_work_years']) {
                $workMatch = false;
            }

            if ($workMatch) {
                $passedChecks++;
                $matchDetails['work_experience'] = ['status' => 'pass', 'message' => "工作经验{$workYears}年符合要求"];
            } else {
                $matchDetails['work_experience'] = ['status' => 'fail', 'message' => "工作经验{$workYears}年不符合要求"];
            }
        }

        // 计算匹配分数
        if ($totalChecks > 0) {
            $score = round(($passedChecks / $totalChecks) * 100, 2);
        }

        return [
            'score' => $score,
            'is_qualified' => $passedChecks == $totalChecks && $totalChecks > 0,
            'match_details' => $matchDetails,
            'passed_checks' => $passedChecks,
            'total_checks' => $totalChecks
        ];
    }

    /**
     * 计算年龄
     */
    private function calculateAge($birthdate)
    {
        return date('Y') - date('Y', strtotime($birthdate));
    }

    /**
     * 检查学历等级
     */
    private function checkEducationLevel($userEducation, $requiredLevel)
    {
        $educationLevels = [
            '中专' => 1, '中技' => 1, '职高' => 1,
            '大专' => 2, '高职' => 2,
            '本科' => 3, '学士' => 3,
            '研究生' => 4, '硕士' => 5, '博士' => 6
        ];

        $userLevel = 0;
        foreach ($educationLevels as $edu => $level) {
            if (strpos($userEducation, $edu) !== false) {
                $userLevel = $level;
                break;
            }
        }

        return $userLevel >= $requiredLevel;
    }

    /**
     * 检查专业匹配
     */
    private function checkMajorMatch($userMajor, $keywords)
    {
        $keywordArray = explode(',', $keywords);
        foreach ($keywordArray as $keyword) {
            $keyword = trim($keyword);
            if (!empty($keyword) && strpos($userMajor, $keyword) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查毕业年限是否符合要求
     */
    private function checkGraduationYears($userJobData, $requiredYears)
    {
        // 获取用户简历中的毕业时间
        $userGraduationDate = isset($userJobData['graduation_date']) ? $userJobData['graduation_date'] : '';

        if (empty($userGraduationDate)) {
            // 如果用户没有填写毕业时间，无法判断，返回false
            return false;
        }

        // 计算用户毕业时间距今的年数
        $graduationTime = strtotime($userGraduationDate . '-01');
        $currentTime = time();
        $yearsDiff = (date('Y', $currentTime) - date('Y', $graduationTime));

        // 如果当前月份小于毕业月份，年数减1
        if (date('m', $currentTime) < date('m', $graduationTime)) {
            $yearsDiff--;
        }

        // 判断用户毕业年数是否在岗位要求的年限内
        return $yearsDiff <= $requiredYears;
    }

    /**
     * 判断是否为应届生
     * 应届生判断逻辑：毕业一年内无工作经历
     */
    private function checkIsFreshGraduate($userJobData)
    {
        // 获取毕业时间
        $graduationDate = isset($userJobData['graduation_date']) ? $userJobData['graduation_date'] : '';
        $workExperienceYears = intval($userJobData['work_experience_years'] ?? 0);

        if (empty($graduationDate)) {
            // 如果没有毕业时间，根据工作经验判断（无工作经验才算应届生）
            return $workExperienceYears == 0;
        }

        // 计算毕业时间距今的月数
        $graduationTime = strtotime($graduationDate . '-01'); // 转换为时间戳
        $currentTime = time();
        $monthsDiff = (date('Y', $currentTime) - date('Y', $graduationTime)) * 12 +
                     (date('m', $currentTime) - date('m', $graduationTime));

        // 应届生判断条件：
        // 1. 毕业时间在12个月内
        // 2. 无工作经验（工作经验为0）
        return $monthsDiff <= 12 && $workExperienceYears == 0;
    }
}
