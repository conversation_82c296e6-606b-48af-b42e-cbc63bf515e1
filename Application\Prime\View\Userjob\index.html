<include file="block/hat" />
<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <style type='text/css'>
                /* 现代化页面容器 */
                .userjob-page-wrapper {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    min-height: 100vh;
                    padding: 0;
                    margin: 0;
                }

                .userjob-page-container {
                    max-width: 1400px;
                    margin: 0 auto;
                    padding: 2rem;
                    background: transparent;
                }

                /* 现代化页面标题 */
                .userjob-page-header {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                    position: relative;
                }

                .userjob-page-header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                }

                .userjob-header-content {
                    padding: 2rem;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .userjob-page-title {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin: 0;
                }

                .userjob-title-icon {
                    width: 3rem;
                    height: 3rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }

                .userjob-title-text {
                    display: flex;
                    flex-direction: column;
                }

                .userjob-title-main {
                    font-size: 2.3rem;
                    font-weight: 700;
                    color: #1a202c;
                    margin: 0;
                    line-height: 1.2;
                }

                .userjob-title-sub {
                    font-size: 1.7rem;
                    color: #718096;
                    margin: 0;
                    font-weight: 400;
                }

                .userjob-header-stats {
                    display: flex;
                    gap: 2rem;
                    align-items: center;
                }

                .userjob-stat-item {
                    text-align: center;
                    padding: 0.75rem 1.5rem;
                    background: #f7fafc;
                    border-radius: 0.75rem;
                    border: 1px solid #e2e8f0;
                    min-width: 5rem;
                }

                .userjob-stat-number {
                    font-size: 2rem;
                    font-weight: 700;
                    color: #667eea;
                    margin: 0;
                    line-height: 1;
                }

                .userjob-stat-label {
                    font-size: 1.7rem;
                    color: #718096;
                    margin: 0.25rem 0 0 0;
                    font-weight: 500;
                }

                /* 页面头部操作按钮 */
                .userjob-header-actions {
                    display: flex;
                    justify-content: center;
                    gap: 1rem;
                    padding: 1.5rem 2rem;
                    background: #f8fafc;
                    border-top: 1px solid #e2e8f0;
                }

                .userjob-header-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.75rem;
                    font-size: 1.7rem;
                    font-weight: 600;
                    text-decoration: none;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: none;
                    cursor: pointer;
                }

                .userjob-header-btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2);
                    text-decoration: none;
                }

                .userjob-header-btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .userjob-header-btn-primary:hover {
                    color: white;
                }

                .userjob-header-btn-success {
                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                    color: white;
                }

                .userjob-header-btn-success:hover {
                    color: white;
                }

                /* 现代化导航标签 */
                .userjob-nav-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .userjob-nav-tabs {
                    display: flex;
                    background: #f8fafc;
                    border-bottom: 1px solid #e2e8f0;
                    margin: 0;
                    padding: 0;
                    list-style: none;
                    overflow-x: auto;
                }

                .userjob-nav-item {
                    flex: 1;
                    min-width: 0;
                }

                .userjob-nav-link {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 1.25rem 1.5rem;
                    color: #718096;
                    text-decoration: none;
                    font-size: 1.5rem;
                    font-weight: 600;
                    border-bottom: 3px solid transparent;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    white-space: nowrap;
                }

                .userjob-nav-link:hover {
                    color: #667eea;
                    background: rgba(102, 126, 234, 0.05);
                    text-decoration: none;
                }

                .userjob-nav-link.active {
                    color: #667eea;
                    background: white;
                    border-bottom-color: #667eea;
                    box-shadow: 0 -2px 4px rgba(102, 126, 234, 0.1);
                }

                .userjob-nav-link.active::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                }

                .userjob-nav-icon {
                    font-size: 1.25rem;
                }

                /* 现代化搜索面板 */
                .userjob-search-container {
                    background: white;
                    border-radius: 1rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                    border: 1px solid #e2e8f0;
                    margin-bottom: 2rem;
                    overflow: hidden;
                }

                .userjob-search-header {
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    padding: 1.5rem 2rem;
                    border-bottom: 1px solid #e2e8f0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    gap: 1rem;
                }

                .userjob-search-title {
                    display: flex;
                    align-items: center;
                    gap: 0.75rem;
                    margin: 0;
                    font-size: 2rem;
                    font-weight: 600;
                    color: #1a202c;
                }

                .userjob-search-icon {
                    width: 2.5rem;
                    height: 2.5rem;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 0.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.25rem;
                }

                .userjob-search-actions {
                    display: flex;
                    gap: 0.75rem;
                    align-items: center;
                }

                .userjob-search-toggle {
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }

                .userjob-search-toggle:hover {
                    background: #5a67d8;
                    transform: translateY(-1px);
                }

                .userjob-search-body {
                    padding: 2rem;
                    background: white;
                }

                .userjob-search-form {
                    display: flex;
                    flex-direction: column;
                    gap: 1.5rem;
                }

                .userjob-search-row {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    align-items: end;
                }

                .userjob-form-group {
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                }

                .userjob-form-label {
                    font-size: 1.7rem;
                    font-weight: 600;
                    color: #374151;
                    margin: 0;
                }

                .userjob-form-control {
                    padding: 0.75rem 1rem;
                    border: 2px solid #e5e7eb;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    transition: all 0.3s ease;
                    background: white;
                    color: #374151;
                }

                .userjob-form-control:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .userjob-form-control::placeholder {
                    color: #9ca3af;
                }

                .userjob-select {
                    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
                    background-position: right 0.5rem center;
                    background-repeat: no-repeat;
                    background-size: 1.5em 1.5em;
                    padding-right: 2.5rem;
                    appearance: none;
                }

                .userjob-search-buttons {
                    display: flex;
                    gap: 1rem;
                    justify-content: flex-end;
                    align-items: center;
                    padding-top: 1.5rem;
                    border-top: 1px solid #e5e7eb;
                    flex-wrap: wrap;
                }

                .userjob-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.7rem;
                    font-weight: 600;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                }

                .userjob-btn:hover {
                    transform: translateY(-1px);
                    text-decoration: none;
                }

                .userjob-btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(102, 126, 234, 0.3);
                }

                .userjob-btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    box-shadow: 0 8px 15px -3px rgba(102, 126, 234, 0.4);
                }

                .userjob-btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(107, 114, 128, 0.3);
                }

                .userjob-btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    box-shadow: 0 8px 15px -3px rgba(107, 114, 128, 0.4);
                }

                .userjob-btn-success {
                    background: #10b981;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
                }

                .userjob-btn-success:hover {
                    background: #059669;
                    color: white;
                    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
                }

                .userjob-btn-info {
                    background: #3b82f6;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
                }

                .userjob-btn-info:hover {
                    background: #2563eb;
                    color: white;
                    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.4);
                }

                .userjob-btn-warning {
                    background: #f59e0b;
                    color: white;
                    box-shadow: 0 4px 6px -1px rgba(245, 158, 11, 0.3);
                }

                .userjob-btn-warning:hover {
                    background: #d97706;
                    color: white;
                    box-shadow: 0 8px 15px -3px rgba(245, 158, 11, 0.4);
                }

                /* 响应式设计 */
                @media (max-width: 1024px) {
                    .userjob-page-container {
                        padding: 1.5rem;
                    }

                    .userjob-header-content {
                        flex-direction: column;
                        text-align: center;
                    }

                    .userjob-header-stats {
                        justify-content: center;
                        flex-wrap: wrap;
                    }

                    .userjob-search-row {
                        grid-template-columns: 1fr;
                    }
                }

                @media (max-width: 768px) {
                    .userjob-page-container {
                        padding: 1rem;
                    }

                    .userjob-nav-tabs {
                        flex-direction: column;
                    }

                    .userjob-nav-item {
                        flex: none;
                    }

                    .userjob-nav-link {
                        justify-content: flex-start;
                        border-bottom: none;
                        border-right: 3px solid transparent;
                    }

                    .userjob-nav-link.active {
                        border-bottom: none;
                        border-right-color: #667eea;
                    }

                    .userjob-search-buttons {
                        justify-content: center;
                    }

                    .userjob-title-main {
                        font-size: 2rem;
                    }

                    .userjob-title-sub {
                        font-size: 1.45rem;
                    }
                }

                /* 保留原有样式 */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .userjob-fade-in {
                    animation: fadeInUp 0.6s ease-out;
                }

                .userjob-fade-in-delay-1 {
                    animation: fadeInUp 0.6s ease-out 0.1s both;
                }

                .userjob-fade-in-delay-2 {
                    animation: fadeInUp 0.6s ease-out 0.2s both;
                }

                .userjob-fade-in-delay-3 {
                    animation: fadeInUp 0.6s ease-out 0.3s both;
                }
                .tab-pane {padding:20px 0 20px 0;}
                .resume-card {
                    border: 1px solid #e3e3e3;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    background: #fff;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                    transition: box-shadow 0.3s ease;
                }
                .resume-card:hover {
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }
                .resume-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 8px 8px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .resume-id {
                    font-size: 1.75rem;
                    font-weight: bold;
                }
                .resume-status {
                    padding: 0.5rem 1rem;
                    border-radius: 1.25rem;
                    font-size: 1.25rem;
                    font-weight: 600;
                    background: rgba(255,255,255,0.2);
                }
                .resume-body {
                    padding: 1.5rem;
                }
                .resume-row {
                    display: flex;
                    flex-wrap: wrap;
                    margin: -0.75rem;
                }
                .resume-col {
                    flex: 1;
                    min-width: 300px;
                    padding: 0.75rem;
                }
                .resume-photo {
                    text-align: center;
                    margin-bottom: 1.25rem;
                }
                .resume-photo img {
                    border-radius: 0.75rem;
                    border: 3px solid #f0f0f0;
                    max-width: 120px;
                    height: auto;
                }
                .info-section {
                    margin-bottom: 1.5rem;
                    overflow: hidden; /* 防止子元素溢出 */
                }

                /* 沟通记录区域特殊处理 */
                .info-section[style*="position: relative"] {
                    padding-right: 10px; /* 为删除按钮预留空间 */
                }
                .info-title {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 0.75rem;
                    padding-bottom: 0.5rem;
                    border-bottom: 2px solid #667eea;
                    display: inline-block;
                }
                .info-content {
                    font-size: 1.375rem;
                    line-height: 1.6;
                    color: #555;
                }
                .info-item {
                    margin-bottom: 0.5rem;
                }
                .info-label {
                    font-weight: 600;
                    color: #333;
                    display: inline-block;
                    min-width: 100px;
                    font-size: 1.375rem;
                }
                /* 求职诉求和服务说明并列显示 */
                .resume-highlight-section {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 1rem;
                    margin-bottom: 15px;
                }

                .highlight-text {
                    font-weight: bold;
                    font-size: 1.5rem;
                    padding: 1rem 1.25rem;
                    border-radius: 0.75rem;
                    border: 1px solid;
                    line-height: 1.5;
                }

                .highlight-demand {
                    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                    border-color: #f59e0b;
                    color: #92400e;
                }

                .highlight-service {
                    background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%);
                    border-color: #0ea5e9;
                    color: #0c4a6e;
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .resume-highlight-section {
                        grid-template-columns: 1fr;
                        gap: 0.5rem;
                    }
                }
                /* 现代化操作按钮样式 */
                .resume-action-buttons {
                    display: flex !important;
                    flex-wrap: wrap;
                    gap: 0.75rem;
                    padding-top: 1.25rem;
                    border-top: 1px solid #e2e8f0;
                    justify-content: flex-end !important;
                    align-items: center;
                    text-align: right !important;
                }

                .resume-action-btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.625rem 1rem;
                    border: none;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    min-height: 2.5rem;
                    font-family: inherit;
                }

                .resume-action-btn:hover {
                    transform: translateY(-1px);
                    text-decoration: none;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }

                .resume-action-btn:active {
                    transform: translateY(0);
                }

                .resume-btn-primary {
                    background: #667eea;
                    color: white;
                    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
                }

                .resume-btn-primary:hover {
                    background: #5a67d8;
                    color: white;
                    box-shadow: 0 6px 12px rgba(102, 126, 234, 0.4);
                }

                .resume-btn-success {
                    background: #10b981;
                    color: white;
                    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
                }

                .resume-btn-success:hover {
                    background: #059669;
                    color: white;
                    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.4);
                }

                .resume-btn-info {
                    background: #3b82f6;
                    color: white;
                    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
                }

                .resume-btn-info:hover {
                    background: #2563eb;
                    color: white;
                    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
                }

                .resume-btn-warning {
                    background: #f59e0b;
                    color: white;
                    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
                }

                .resume-btn-warning:hover {
                    background: #d97706;
                    color: white;
                    box-shadow: 0 6px 12px rgba(245, 158, 11, 0.4);
                }

                .resume-btn-secondary {
                    background: #6b7280;
                    color: white;
                    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
                }

                .resume-btn-secondary:hover {
                    background: #4b5563;
                    color: white;
                    box-shadow: 0 6px 12px rgba(107, 114, 128, 0.4);
                }

                .resume-btn-danger {
                    background: #ef4444;
                    color: white;
                    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
                }

                .resume-btn-danger:hover {
                    background: #dc2626;
                    color: white;
                    box-shadow: 0 6px 12px rgba(239, 68, 68, 0.4);
                }

                /* 响应式设计 */
                @media (max-width: 768px) {
                    .resume-action-buttons {
                        flex-direction: column;
                        align-items: stretch;
                        gap: 0.5rem;
                    }

                    .resume-action-btn {
                        justify-content: center;
                        width: 100%;
                    }
                }

                /* 强制居右对齐 */
                .resume-card .resume-action-buttons {
                    justify-content: flex-end !important;
                    text-align: right !important;
                }

                @media (max-width: 1024px) {
                    .resume-action-buttons {
                        justify-content: flex-end !important;
                    }

                    .resume-action-btn {
                        font-size: 1.125rem;
                        padding: 0.5rem 0.875rem;
                    }
                }
                .search-panel {
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    border: none;
                    border-radius: 10px;
                    margin-bottom: 25px;
                }
                .search-form {
                    padding: 20px;
                }
                .search-form .form-group {
                    margin-right: 15px;
                    margin-bottom: 10px;
                    display: inline-flex;
                    align-items: center;
                }
                .search-form .control-label {
                    margin-right: 0.75rem;
                    margin-bottom: 0;
                    white-space: nowrap;
                    font-weight: 600;
                    color: #555;
                    font-size: 1.375rem;
                }
                .search-form .form-control {
                    border-radius: 0.5rem;
                    border: 1px solid #ddd;
                    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
                    transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
                    font-size: 1.375rem;
                    padding: 0.75rem 1rem;
                }
                .search-form .form-control:focus {
                    border-color: #667eea;
                    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 0 3px rgba(102, 126, 234, 0.1);
                }
                .search-form .btn {
                    border-radius: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    margin-right: 0.75rem;
                    font-size: 1.375rem;
                }
                .search-form .row {
                    margin-left: -5px;
                    margin-right: -5px;
                }
                .search-form .row > div {
                    padding-left: 5px;
                    padding-right: 5px;
                }
                .file-links {
                    text-align: center;
                }
                .file-links a {
                    display: inline-block;
                    margin: 0.375rem;
                    padding: 0.75rem 1rem;
                    background: #3498db;
                    color: white;
                    text-decoration: none;
                    border-radius: 0.5rem;
                    font-size: 1.25rem;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }
                .file-links a:hover {
                    background: #2980b9;
                    color: white;
                    text-decoration: none;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                }
                .recent-message {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 0.75rem;
                    padding: 1rem;
                    margin-top: 1rem;
                    position: relative;
                }
                .recent-message-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.75rem;
                    text-align: left; /* 确保头部内容左对齐 */
                }
                .message-type {
                    padding: 0.375rem 0.75rem;
                    border-radius: 1rem;
                    font-size: 1.125rem;
                    font-weight: bold;
                }
                .message-type-platform {
                    background: #e74c3c;
                    color: white;
                }
                .message-type-station {
                    background: #27ae60;
                    color: white;
                }
                .message-type-zjb {
                    background: #f39c12;
                    color: white;
                }
                .message-type-unknown {
                    background: #95a5a6;
                    color: white;
                }
                .message-time {
                    font-size: 1.125rem;
                    color: #6c757d;
                    font-weight: 500;
                }
                .message-content {
                    font-size: 1.375rem;
                    line-height: 1.5;
                    color: #495057;
                    max-height: 80px;
                    overflow: hidden;
                    position: relative;
                    word-wrap: break-word; /* 强制换行 */
                    word-break: break-word; /* 在单词边界断行，保持可读性 */
                    white-space: normal; /* 正常的空格处理 */
                    max-width: 100%; /* 确保不超出容器 */
                    text-align: left; /* 明确指定左对齐 */
                }
                .message-content::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 30px;
                    height: 20px;
                    background: linear-gradient(to right, transparent, #f8f9fa);
                }
                .no-recent-message {
                    text-align: center;
                    color: #6c757d;
                    font-style: italic;
                    padding: 1.5rem;
                    background: #f8f9fa;
                    border-radius: 0.75rem;
                    margin-top: 1rem;
                    font-size: 1.375rem;
                }
                .message-actions {
                    text-align: right;
                    margin-top: 0.75rem;
                }
                .message-actions a {
                    font-size: 1.25rem;
                    color: #007bff;
                    text-decoration: none;
                    font-weight: 500;
                }
                .message-actions a:hover {
                    text-decoration: underline;
                }
                .need-reply-indicator {
                    position: absolute;
                    top: -0.375rem;
                    right: -0.375rem;
                    background: #e74c3c;
                    color: white;
                    border-radius: 50%;
                    width: 1.5rem;
                    height: 1.5rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 0.875rem;
                    font-weight: bold;
                    animation: pulse 2s infinite;
                }
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                    100% { transform: scale(1); }
                }
                .message-count-badge {
                    background: #17a2b8;
                    color: white;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.75rem;
                    font-size: 1rem;
                    font-weight: bold;
                    margin-left: 0.375rem;
                }
                .quick-reply-btn {
                    background: #28a745;
                    color: white;
                    border: none;
                    padding: 0.5rem 0.75rem;
                    border-radius: 0.375rem;
                    font-size: 1.25rem;
                    margin-left: 0.75rem;
                    cursor: pointer;
                    font-weight: 500;
                    transition: all 0.3s ease;
                }
                .quick-reply-btn:hover {
                    background: #218838;
                    color: white;
                    transform: translateY(-1px);
                }

                /* 快速回复表单样式 */
                .quick-reply-form {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    margin-top: 15px;
                    display: none;
                }
                .quick-reply-form.show {
                    display: block;
                    animation: slideDown 0.3s ease-out;
                }
                @keyframes slideDown {
                    from {
                        opacity: 0;
                        transform: translateY(-10px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                .quick-reply-textarea {
                    width: 100%;
                    min-height: 100px;
                    border: 1px solid #ced4da;
                    border-radius: 0.5rem;
                    padding: 0.75rem;
                    font-size: 1.375rem;
                    resize: vertical;
                    font-family: inherit;
                    line-height: 1.5;
                }
                .quick-reply-textarea:focus {
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    outline: none;
                }
                .quick-reply-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 10px;
                }
                .quick-reply-submit {
                    background: #667eea;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    cursor: pointer;
                    transition: background 0.3s ease;
                }
                .quick-reply-submit:hover {
                    background: #5a67d8;
                }
                .quick-reply-submit:disabled {
                    background: #6c757d;
                    cursor: not-allowed;
                }
                .quick-reply-cancel {
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    cursor: pointer;
                    margin-right: 10px;
                }
                .quick-reply-cancel:hover {
                    background: #5a6268;
                }
                .need-reply-checkbox {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    font-size: 12px;
                    color: #6c757d;
                }
                .need-reply-checkbox input[type="checkbox"] {
                    margin: 0;
                }

                /* 多条消息显示样式 */
                .messages-list {
                    max-height: 300px;
                    overflow-y: auto;
                    overflow-x: hidden; /* 防止横向滚动条 */
                    padding-right: 5px; /* 为删除按钮留出空间 */
                    text-align: left; /* 确保消息列表左对齐 */
                    direction: ltr; /* 明确指定文本方向 */
                }
                .message-item {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    padding: 10px;
                    padding-right: 15px; /* 为删除按钮留出更多空间 */
                    margin-bottom: 10px;
                    margin-right: 5px; /* 防止删除按钮被截断 */
                    position: relative;
                    text-align: left; /* 确保整个消息项左对齐 */
                    direction: ltr; /* 明确指定文本方向为从左到右 */
                }

                /* 消息删除按钮 */
                .message-delete-btn {
                    position: absolute;
                    top: 4px; /* 下移10px：从-6px调整到4px */
                    right: -3px; /* 减少右侧偏移，防止超出容器 */
                    width: 18px; /* 稍微减小尺寸 */
                    height: 18px;
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    font-size: 11px; /* 调整字体大小 */
                    cursor: pointer;
                    display: none;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.2s ease;
                    z-index: 10;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                }

                .message-delete-btn:hover {
                    background: #dc2626;
                    transform: scale(1.1);
                }

                .message-item:hover .message-delete-btn {
                    display: flex;
                }

                /* 防止整个沟通记录区域出现横向滚动条 */
                .info-section h4:contains("沟通记录") + div,
                .info-section h4:contains("沟通记录") + * {
                    overflow-x: hidden !important;
                    max-width: 100%;
                }

                /* 通用防溢出样式 */
                .recent-message-header,
                .recent-message-header * {
                    word-wrap: break-word;
                    word-break: break-word; /* 改为break-word，保持可读性 */
                    max-width: 100%;
                    box-sizing: border-box;
                    text-align: left; /* 确保左对齐 */
                }
                .message-item:last-child {
                    margin-bottom: 0;
                }
                .message-expand-btn {
                    background: none;
                    border: none;
                    color: #007bff;
                    font-size: 11px;
                    cursor: pointer;
                    padding: 0;
                    text-decoration: underline;
                }
                .message-expand-btn:hover {
                    color: #0056b3;
                }
                .message-content.collapsed {
                    max-height: 40px;
                    overflow: hidden;
                    position: relative;
                    word-wrap: break-word; /* 强制换行 */
                    word-break: break-word; /* 在单词边界断行，保持可读性 */
                    white-space: normal; /* 正常的空格处理 */
                    max-width: 100%; /* 确保不超出容器 */
                    text-align: left; /* 明确指定左对齐 */
                }
                .message-content.collapsed::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    width: 50px;
                    height: 20px;
                    background: linear-gradient(to right, transparent, #f8f9fa);
                }
                @media (max-width: 768px) {
                    .resume-col {
                        min-width: 100%;
                    }
                    .resume-header {
                        flex-direction: column;
                        text-align: center;
                    }
                    .resume-id {
                        margin-bottom: 10px;
                    }

                    /* 移动端消息删除按钮优化 */
                    .messages-list {
                        padding-right: 8px; /* 移动端增加更多右侧空间 */
                    }

                    .message-item {
                        padding-right: 20px; /* 移动端为删除按钮留出更多空间 */
                        margin-right: 8px;
                    }

                    .message-delete-btn {
                        top: 5px; /* 移动端下移10px：从-5px调整到5px */
                        right: -2px;
                        width: 20px; /* 移动端稍大一些便于点击 */
                        height: 20px;
                        font-size: 12px;
                    }

                    /* 移动端头部按钮调整 */
                    .userjob-header-actions {
                        flex-direction: column;
                        gap: 0.75rem;
                        padding: 1rem;
                    }

                    .userjob-header-btn {
                        width: 100%;
                        justify-content: center;
                    }

                    /* 移动端统计区域调整 */
                    .userjob-header-stats {
                        flex-direction: column;
                        gap: 1rem;
                        text-align: center;
                    }
                }
            </style>

            <div class="userjob-page-wrapper">
                <div class="userjob-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="userjob-page-header userjob-fade-in">
                        <div class="userjob-header-content">
                            <div class="userjob-page-title">
                                <div class="userjob-title-icon">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="userjob-title-text">
                                    <h1 class="userjob-title-main">简历管理系统</h1>
                                    <p class="userjob-title-sub">Resume Management System</p>
                                </div>
                      <div style="text-align: center;margin-left: 168px;">
                            <a href="{:U('userjob/edit')}" class="userjob-header-btn userjob-header-btn-primary">
                                <i class="fa fa-plus"></i>
                                <span>添加简历</span>
                            </a>
                            <a href="{:U('userjob/upjob')}" class="userjob-header-btn userjob-header-btn-success" style="margin-left: 28px;">
                                <i class="fa fa-upload"></i>
                                <span>上传简历文件</span>
                            </a>

                        </div>
                            </div>
                            <div class="userjob-header-stats">
                                <div class="userjob-stat-item">
                                    <div class="userjob-stat-number" id="total-resumes">{$statsData.total_resumes|default=0}</div>
                                    <div class="userjob-stat-label">总简历</div>
                                </div>
                                <div class="userjob-stat-item">
                                    <div class="userjob-stat-number" id="active-resumes">{$statsData.active_resumes|default=0}</div>
                                    <div class="userjob-stat-label">沟通中</div>
                                </div>
                                <div class="userjob-stat-item">
                                    <div class="userjob-stat-number" id="need-reply">{$statsData.need_reply|default=0}</div>
                                    <div class="userjob-stat-label">待回复</div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮区域 -->

                        </div>
                    </div>
 

                    <!-- 现代化搜索面板 -->
                    <div class="userjob-fade-in-delay-2">
                        <div class="userjob-search-header">
                            <div class="userjob-search-title">
                                <div class="userjob-search-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <span>高级搜索</span>
                            </div>
            
                            <div class="userjob-search-actions">
                                <button type="button" class="userjob-search-toggle" onclick="toggleSearchPanel()">
                                    <i class="fa fa-filter"></i>
                                    <span>筛选条件</span>
                                </button>
                            </div>
                        </div>
                        <div class="userjob-search-body" id="search-panel" style="display: none;">
                            <form method="get" class="userjob-search-form" role="form">
                                <!-- 第一行：基础搜索 -->
                                <div class="userjob-search-row">
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">搜索条件</label>
                                        <select class="userjob-form-control userjob-select" name="kw">
                                            <php>foreach($c_kw as $key=>$value){</php>
                                            <option value="{$key}" <php> if($key == $_get['kw']){</php>selected<php>}</php>>{$value}</option>
                                            <php>}</php>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">搜索内容</label>
                                        <input class="userjob-form-control" type="text" name="val" value="{$_get.val}" placeholder="请输入搜索内容" />
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">简历状态</label>
                                        <select name="status" class='userjob-form-control userjob-select'>
                                            <option value="">全部状态</option>
                                            <option value="0" <php>if($_get['status'] === '0') echo 'selected';</php>>沟通中</option>
                                            <option value="1" <php>if($_get['status'] === '1') echo 'selected';</php>>培训中</option>
                                            <option value="2" <php>if($_get['status'] === '2') echo 'selected';</php>>已入职</option>
                                            <option value="3" <php>if($_get['status'] === '3') echo 'selected';</php>>服务终止</option>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">服务站</label>
                                        <select name="service_station_id" class='userjob-form-control userjob-select'>
                                            <option value="">全部服务站</option>
                                            <php>if(isset($serviceStationListForSelect)) foreach($serviceStationListForSelect as $stationId => $stationName) {</php>
                                            <option value="{$stationId}" <php>if($_get['service_station_id'] == $stationId) echo 'selected';</php>>{$stationName}</option>
                                            <php>}</php>
                                        </select>
                                    </div>
                                </div>

                                <!-- 第二行：高级筛选 -->
                                <div class="userjob-search-row">
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">性别</label>
                                        <select name="gender" class='userjob-form-control userjob-select'>
                                            <option value="">不限</option>
                                            <option value="男" <php>if($_get['gender'] == '男') echo 'selected';</php>>男</option>
                                            <option value="女" <php>if($_get['gender'] == '女') echo 'selected';</php>>女</option>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">学历</label>
                                        <select name="education_level" class='userjob-form-control userjob-select'>
                                            <option value="">不限学历</option>
                                            <option value="初中" <php>if($_get['education_level'] == '初中') echo 'selected';</php>>初中</option>
                                            <option value="高中" <php>if($_get['education_level'] == '高中') echo 'selected';</php>>高中</option>
                                            <option value="中专" <php>if($_get['education_level'] == '中专') echo 'selected';</php>>中专</option>
                                            <option value="大专" <php>if($_get['education_level'] == '大专') echo 'selected';</php>>大专</option>
                                            <option value="本科" <php>if($_get['education_level'] == '本科') echo 'selected';</php>>本科</option>
                                            <option value="硕士" <php>if($_get['education_level'] == '硕士') echo 'selected';</php>>硕士</option>
                                            <option value="博士" <php>if($_get['education_level'] == '博士') echo 'selected';</php>>博士</option>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">是否恐高</label>
                                        <select name="is_afraid_heights" class='userjob-form-control userjob-select'>
                                            <option value="">不限</option>
                                            <option value="1" <php>if($_get['is_afraid_heights'] == '1') echo 'selected';</php>>恐高</option>
                                            <option value="2" <php>if($_get['is_afraid_heights'] == '2') echo 'selected';</php>>不恐高</option>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">培训报名</label>
                                        <select name="has_training" class='userjob-form-control userjob-select'>
                                            <option value="">不限</option>
                                            <option value="1" <php>if($_get['has_training'] == '1') echo 'selected';</php>>是</option>
                                            <option value="0" <php>if($_get['has_training'] == '0') echo 'selected';</php>>否</option>
                                        </select>
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">添加时间</label>
                                        <input type="text" name="time[start]" value="{$_get.time.start}" placeholder="开始时间" class="userjob-form-control datetimepicker" autocomplete="off">
                                    </div>
                                    <div class="userjob-form-group">
                                        <label class="userjob-form-label">结束时间</label>
                                        <input type="text" name="time[end]" value="{$_get.time.end}" placeholder="结束时间" class="userjob-form-control datetimepicker" autocomplete="off">
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="userjob-search-buttons">
                                        <a href="{:U('userjob/index')}" class="userjob-btn userjob-btn-secondary">
                                        <i class="fa fa-refresh"></i>
                                        重置
                                    </a>
                                    <button type="submit" class="userjob-btn userjob-btn-primary">
                                        <i class="fa fa-search"></i>
                                        搜索
                                    </button>
                                
                                 
                                </div>
                            </form>
                        </div>
                    </div>
                <!-- 简历列表 -->
                <form action="" method="post">
                    <php>foreach($list as $v) { </php>
                    <div class="resume-card">
                        <!-- 卡片头部 -->
                        <div class="resume-header">
                            <div class="resume-id">
                                <i class="fa fa-user"></i> 简历 #{$v.id}
                                        <span confirm="true" url="{:U('userjob/deljob', ['id' => $v['id']])}" class="resume-action-btn resume-btn-danger js_ajax" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #ef4444; color: white; box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);">
                                    <i class="fa fa-trash"></i>
                                    <span>删除</span>
                                </span>
                            </div>
                            <div class="resume-status">
                                <php>
                                $jobState = isset($v['job_state']) ? $v['job_state'] : '0';
                                $statusText = '';
                                switch($jobState) {
                                    case '0': $statusText = '沟通中'; break;
                                    case '1': $statusText = '培训中'; break;
                                    case '2': $statusText = '已入职'; break;
                                    case '3': $statusText = '服务终止'; break;
                                    default: $statusText = '沟通中';
                                }
                                echo $statusText;
                                </php>
                            </div>
                        </div>

                        <!-- 卡片主体 -->
                        <div class="resume-body">
                            <!-- 求职诉求和服务说明并列显示 -->
                            <div class="resume-highlight-section">
                              
                                <div class="highlight-text highlight-demand">
                                    <i class="fa fa-star"></i> <strong>求职诉求：</strong>
                                      <php>if($v['remark']) { </php>
                                    {$v.remark}
                                    <php>}else{</php>
                                        无
                                    <php>}</php>
                                </div>
                                

                                
                                <div class="highlight-text highlight-service">
                                    <i class="fa fa-info-circle"></i> <strong>服务说明：</strong>
                                    <php>
                                $jobState = isset($v['job_state']) ? $v['job_state'] : '0';
                                $statusText = '';
                                switch($jobState) {
                                    case '0': $statusText = '沟通中'; break;
                                    case '1': $statusText = '培训中'; break;
                                    case '2': $statusText = '已入职'; break;
                                    case '3': $statusText = '服务终止'; break;
                                    default: $statusText = '沟通中';
                                }
                                echo $statusText;
                                </php>
                                    <php>if($v['job_state_text']) { </php>
                                       （{$v.job_state_text}） 
                                    <php>}</php>
                                </div>
                                
                            </div>

                            <div class="resume-row">
                                <!-- 照片和文件列 -->
                                <div class="resume-col" style="flex: 0 0 200px;">
                                    <div class="info-section">
                                        <div class="info-title">
                                            <i class="fa fa-camera"></i> 照片与文件
                                        </div>
                                        <div class="resume-photo">
                                            <php>if (!empty($v['photo_path'])) {</php>
                                            <a target="_blank" href="http://we.zhongcaiguoke.com/{:$v['photo_path']}">
                                                <img src="http://we.zhongcaiguoke.com/{:$v['photo_path']}" alt="个人照片">
                                            </a>
                                            <php>} else {</php>
                                            <div style="width: 100px; height: 120px; background: #f5f5f5; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                                <i class="fa fa-user" style="font-size: 40px; color: #ccc;"></i>
                                            </div>
                                            <php>}</php>
                                        </div>
                                        <div class="file-links">
                                            <a target="_blank" href="http://we.zhongcaiguoke.com/{:$userJobDocList[$v['id']]['content']}">
                                                <i class="fa fa-file-text"></i> 简历文件
                                            </a>
                                            <php>if ($userJobDocList[$v['id']]['is_html'] == 3){</php>
                                            <a target="_blank" href="{:U('userjob/h5', ['id' => $v['id']])}">
                                                <i class="fa fa-mobile"></i> H5简历
                                            </a>
                                            <php>}</php>
                                        </div>
                                    </div>
                                </div>

                                <!-- 个人信息列 -->
                                <div class="resume-col">
                                    <div class="info-section">
                                        <div class="info-title">
                                            <i class="fa fa-user"></i> 个人信息
                                        </div>
                                        <div class="info-content">
                                            <div class="info-item">
                                                <span class="info-label">微信昵称：</span>{$v.name}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">姓名：</span>{$v.name}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">电话：</span>{$v.phone}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">生日：</span>{$v.birthdate}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">性别：</span>{$v.gender}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身份证：</span>{$v.id_number}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">民族：</span>{$v.nation}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">婚姻状况：</span>{$v.marital_status}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">服务站<php>if($serviceStationList[$v['service_station_id']]['zsb_type']==2) echo '<span class="quick-reply-btn">招</span>';</php>：</span>{:$serviceStationList[$v['service_station_id']]['service_name']} 
                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 身体状况列 -->
                                <div class="resume-col">
                                    <div class="info-section">
                                        <div class="info-title">
                                            <i class="fa fa-heartbeat"></i> 身体状况
                                        </div>
                                        <div class="info-content">
                                            <div class="info-item">
                                                <span class="info-label">政治面貌：</span>{$v.political_status}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">健康状况：</span>{$v.health_status}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">身高：</span>{$v.height} cm
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">体重：</span>{$v.weight} kg
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">视力：</span>{$v.vision}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">听力：</span>{$v.hearing}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">是否恐高：</span>
                                                <php>
                                                if($v['is_afraid_heights'] == 1) echo '恐高';
                                                elseif($v['is_afraid_heights'] == 2) echo '不恐高';
                                                else echo '未选择';
                                                </php>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 教育信息列 -->
                                <div class="resume-col">
                                    <div class="info-section">
                                        <div class="info-title">
                                            <i class="fa fa-graduation-cap"></i> 教育与工作
                                        </div>
                                        <div class="info-content">
                                            <div class="info-item">
                                                <span class="info-label">学历：</span>{$v.education_level}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">毕业院校：</span>{$v.graduate_school}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">专业：</span>{$v.major}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">工作经验：</span>{$v.work_experience_years}年
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">应聘职位：</span>{$v.applied_position}
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label">添加时间：</span>{:date('Y-m-d H:i:s', $v['create_time'])}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                       <!-- 操作按钮 -->
                            <div style="width:100%;display: flex; justify-content: flex-end; flex-wrap: wrap; gap: 0.75rem; padding-top: 1.25rem; border-top: 1px solid #e2e8f0; align-items: center;">
                                <a href="{:U('userjob/edit', ['id' => $v['id']])}" class="resume-action-btn resume-btn-primary" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #667eea; color: white; box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);">
                                    <i class="fa fa-edit"></i>
                                    <span>编辑</span>
                                </a>
                                <php>if(isset($trainingOrderList[$v['id']])) {</php>
                                <a href="{:U('training/detail', ['id' => $trainingOrderList[$v['id']]])}" class="resume-action-btn resume-btn-training" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #059669; color: white; box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);">
                                    <i class="fa fa-graduation-cap"></i>
                                    <span>培训详情</span>
                                </a>
                                <php>} else {</php>
                                <a href="javascript:void(0);" onclick="baoming('{$v['id']}');" class="resume-action-btn resume-btn-training" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #ea580c; color: white; box-shadow: 0 2px 4px rgba(234, 88, 12, 0.3);">
                                    <i class="fa fa-plus"></i>
                                    <span>报名培训</span>
                                </a>
                                <php>}</php>
                                <a href="{:U('userjob/upjob', ['id' => $v['id']])}" class="resume-action-btn resume-btn-info" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #3b82f6; color: white; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                                    <i class="fa fa-upload"></i>
                                    <span>更新简历文件</span>
                                </a>
                                <a href="{:U('userjob/joballinfo', ['id' => $v['id']])}" class="resume-action-btn resume-btn-success" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #10b981; color: white; box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);">
                                    <i class="fa fa-info-circle"></i>
                                    <span>查看更多信息</span>
                                </a>
                                <a href="{:U('userjob/upstatus', ['job_id' => $v['id']])}" class="resume-action-btn resume-btn-warning" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #f59e0b; color: white; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                                    <i class="fa fa-refresh"></i>
                                    <span>重新处理简历</span>
                                </a>
                                <a href="{:U('userjob/uph5', ['job_id' => $v['id']])}" class="resume-action-btn resume-btn-secondary" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #6b7280; color: white; box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);">
                                    <i class="fa fa-code"></i>
                                    <span>重新H5</span>
                                </a>
                                <a href="{:U('message/reply', ['user_job_id' => $v['id']])}" class="resume-action-btn resume-btn-info" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 0.625rem 1rem; border: none; border-radius: 0.5rem; font-size: 1.25rem; font-weight: 500; text-decoration: none; cursor: pointer; transition: all 0.3s ease; white-space: nowrap; min-height: 2.5rem; background: #3b82f6; color: white; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                                    <i class="fa fa-comments"></i>
                                    <span>沟通记录</span>
                                </a>
                              
                            </div>

                            </div>

                            <!-- 最近沟通记录 -->
                            <div class="info-section" style="position: relative;margin-top: 20px;">
                                <div class="info-title">
                                    <i class="fa fa-comments"></i> 沟通记录
                                    <php>if(isset($messageCountList[$v['id']])) {</php>
                                    <span class="message-count-badge">{$messageCountList[$v['id']]}</span>
                                    <php>}</php>
                                </div>

                                <!-- 待回复提醒 -->
                                <php>if($v['need_reply'] == 1) {</php>
                                <div class="need-reply-indicator" title="需要回复">
                                    <i class="fa fa-exclamation"></i>
                                </div>
                                <php>}</php>

                                <!-- 显示最近5条沟通记录 -->
                                <php>if(isset($recentMessagesList[$v['id']]) && !empty($recentMessagesList[$v['id']])) {</php>
                                <div class="messages-list">
                                    <php>foreach($recentMessagesList[$v['id']] as $msgIndex => $recentMsg) {</php>
                                    <php>
                                    // 获取发送者身份信息
                                    $senderInfo = '';
                                    $senderIcon = '';
                                    $senderClass = '';

                                    if ($recentMsg['type'] == 2) {
                                        // 平台发送的消息
                                        $senderInfo = '平台';
                                        $senderIcon = 'desktop';
                                        $senderClass = 'message-type-platform';
                                    } else {
                                        // 服务站或招就办发送的消息
                                        if (isset($messageServiceStationMap[$recentMsg['service_station_id']])) {
                                            $station = $messageServiceStationMap[$recentMsg['service_station_id']];
                                            if ($station['zsb_type'] == 1) {
                                                $senderInfo = '服务站';
                                                $senderIcon = 'building';
                                                $senderClass = 'message-type-station';
                                            } else {
                                                $senderInfo = '招就办';
                                                $senderIcon = 'graduation-cap';
                                                $senderClass = 'message-type-zjb';
                                            }
                                        } else {
                                            $senderInfo = '未知';
                                            $senderIcon = 'question';
                                            $senderClass = 'message-type-unknown';
                                        }
                                    }
                                    </php>
                                    <div class="message-item" data-message-id="{$recentMsg.id}">
                                        <button class="message-delete-btn" onclick="deleteUserJobMessage('{$recentMsg.id}', '{$v.id}')" title="删除消息">
                                            <i class="fa fa-times"></i>
                                        </button>
                                        <div class="recent-message-header">
                                            <span class="message-type {:$senderClass}">
                                                <i class="fa fa-{:$senderIcon}"></i>
                                                {:$senderInfo}
                                            </span>
                                            <span class="message-time">
                                                <i class="fa fa-clock-o"></i>
                                                {:date('m-d H:i', $recentMsg['create_time'])}
                                            </span>
                                        </div>
                                        <div class="message-content <php>echo strlen($recentMsg['content']) > 100 ? 'collapsed' : '';</php>" id="msg-content-{$v.id}-{$msgIndex}">
                                            {:strip_tags(htmlspecialchars_decode($recentMsg['content']))}
                                        </div>
                                        <php>if(strlen($recentMsg['content']) > 100) {</php>
                                        <div style="text-align: right; margin-top: 5px;">
                                            <button class="message-expand-btn" onclick="toggleMessageContent('{$v.id}-{$msgIndex}')">
                                                展开全文
                                            </button>
                                        </div>
                                        <php>}</php>
                                    </div>
                                    <php>}</php>
                                </div>

                                <!-- 查看更多消息链接 -->
                                <php>if(isset($messageCountList[$v['id']]) && $messageCountList[$v['id']] > 5) {</php>
                                <div style="text-align: center; margin-top: 10px;">
                                    <a href="{:U('message/index', ['kw' => 'user_job_id', 'val' => $v['id']])}" target="_blank" style="font-size: 12px; color: #007bff;">
                                        <i class="fa fa-external-link"></i> 查看全部 {$messageCountList[$v['id']]} 条记录
                                    </a>
                                </div>
                                <php>}</php>

                                <!-- 快速回复按钮 -->
                                <div style="text-align: center; margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee;">
                                    <button class="quick-reply-btn" onclick="showQuickReplyForm('{$v.id}')">
                                        <i class="fa fa-reply"></i> 快速回复
                                    </button>
                                    <a href="{:U('message/reply', ['user_job_id' => $v['id']])}" target="_blank" style="font-size: 12px; color: #007bff; margin-left: 10px;">
                                        <i class="fa fa-external-link"></i> 查看完整对话
                                    </a>
                                </div>

                                <php>} else {</php>
                                <div class="no-recent-message">
                                    <i class="fa fa-comment-o"></i> 暂无沟通记录
                                    <br>
                                    <button class="quick-reply-btn" onclick="showQuickReplyForm('{$v.id}')" style="margin-top: 10px;">
                                        <i class="fa fa-plus"></i> 开始沟通
                                    </button>
                                </div>
                                <php>}</php>

                                <!-- 快速回复表单 -->
                                <div class="quick-reply-form" id="quick-reply-form-{$v.id}">
                                    <div style="margin-bottom: 10px;">
                                        <strong><i class="fa fa-reply"></i> 快速回复 - 简历 #{$v.id}</strong>
                                    </div>
                                    <form onsubmit="return false;">
                                        <textarea class="quick-reply-textarea" id="reply-content-{$v.id}" placeholder="请输入回复内容..." required></textarea>
                                        <div class="quick-reply-actions">
                                            <div class="need-reply-checkbox">
                                                <input type="checkbox" id="need-reply-{$v.id}" value="1">
                                                <label for="need-reply-{$v.id}">提醒服务站回复</label>
                                                <!-- 微信通知选项 - 仅在有群聊配置时显示 -->
                                                 <php>
                                                    
                                                 </php>
                                                <php>if (!empty($serviceStationListWithWechat[$v['service_station_id']]['chatroom']) && !empty($serviceStationListWithWechat[$v['service_station_id']]['wxatuserlist'])) {</php>
                                                <div style="margin-left: 20px;">
                                                    <input type="checkbox" id="send-wechat-{$v.id}" value="1" checked>
                                                    <label for="send-wechat-{$v.id}">发送微信通知</label>
                                                </div>
                                                <php>}</php>
                                            </div>
                                            <div>
                                                <button type="button" class="quick-reply-cancel" onclick="hideQuickReplyForm('{$v.id}')">
                                                    取消
                                                </button>
                                                <button type="button" class="quick-reply-submit" id="submit-btn-{$v.id}" onclick="submitQuickReply('{$v.id}', event);">
                                                    <i class="fa fa-send"></i> 发送回复
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                     
                           
                        </div>
                    </div>
                    <php>}</php>

                    <!-- 分页 -->
                    <div style="text-align: center; margin-top: 30px;">
                        {$page}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<include file="block/footer" />

<script>
	// 全局函数定义 - 必须在require之外，以便HTML事件可以访问

	// 显示快速回复表单
	function showQuickReplyForm(userJobId) {
		var $form = $('#quick-reply-form-' + userJobId);
		$form.addClass('show');
		$('#reply-content-' + userJobId).focus();
	}

	// 隐藏快速回复表单
	function hideQuickReplyForm(userJobId) {
		var $form = $('#quick-reply-form-' + userJobId);
		$form.removeClass('show');
		$('#reply-content-' + userJobId).val('');
		$('#need-reply-' + userJobId).prop('checked', false);
	}

	// 测试快速回复接口
	function testQuickReplyAPI() {
		var testUrl = '{:U("userjob/quickReply")}?test=1';
		console.log('Testing QuickReply API:', testUrl);

		$.ajax({
			url: testUrl,
			type: 'GET',
			dataType: 'json',
			success: function(response) {
				console.log('API Test Success:', response);
				alert('接口测试成功：' + JSON.stringify(response));
			},
			error: function(xhr, status, error) {
				console.error('API Test Failed:', {
					status: status,
					error: error,
					responseText: xhr.responseText,
					statusCode: xhr.status
				});
				alert('接口测试失败：' + status + ' - ' + error + '\n状态码：' + xhr.status + '\n响应：' + xhr.responseText);
			}
		});
	}

	// 提交快速回复
	function submitQuickReply(userJobId, event) {
		// 立即阻止表单默认提交
		if (event) {
			event.preventDefault();
		}

		var content = $('#reply-content-' + userJobId).val().trim();
		var needReply = $('#need-reply-' + userJobId).is(':checked') ? 1 : 0;
		var sendWechat = $('#send-wechat-' + userJobId).is(':checked') ? 1 : 0;
		var $submitBtn = $('#submit-btn-' + userJobId);
		var $textarea = $('#reply-content-' + userJobId);

		// 验证输入内容
		if (!content) {
			alert('请输入回复内容');
			$textarea.focus();
			return false;
		}

		if (content.length > 1000) {
			alert('回复内容不能超过1000个字符');
			$textarea.focus();
			return false;
		}

		// 禁用提交按钮和文本框
		$submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 发送中...');
		$textarea.prop('disabled', true);

		var requestUrl = '{:U("userjob/quickReply")}';

		$.ajax({
			url: requestUrl,
			type: 'POST',
			data: {
				user_job_id: userJobId,
				content: content,
				need_reply: needReply,
				send_wechat: sendWechat
			},
			dataType: 'json',
			timeout: 30000,
			success: function(response) {

				if (response && response.status == 1) {
					//alert('回复成功！');
					hideQuickReplyForm(userJobId);
					// 刷新页面以显示新的消息
					window.location.reload();
				} else {
					var errorMsg = '回复失败';
					if (response && response.info) {
						errorMsg += '：' + response.info;
					} else {
						errorMsg += '：未知错误';
					}
					alert(errorMsg);
				}
			},
			error: function(xhr, status, error) {

				var errorMsg = '网络错误，请重试';
				if (status === 'timeout') {
					errorMsg = '请求超时，请重试';
				} else if (xhr.status === 404) {
					errorMsg = '接口不存在，请联系管理员';
				} else if (xhr.status === 500) {
					errorMsg = '服务器错误，请重试';
				}
				alert(errorMsg);
			},
			complete: function() {
				// 恢复提交按钮和文本框
				$submitBtn.prop('disabled', false).html('<i class="fa fa-send"></i> 发送回复');
				$textarea.prop('disabled', false);
			}
		});

		// 阻止表单默认提交
		return false;
	}

	// 全局函数定义完成

	require(["daterangepicker", "datetimepicker"], function($){
		$('.js_status').click(function() {
			var that = $(this),
				url = that.attr('url');
			$.get(url, function(data) {
				window.location.reload();
			});
		});

		// 添加卡片动画效果
		$('.resume-card').hover(
			function() {
				$(this).css('transform', 'translateY(-2px)');
			},
			function() {
				$(this).css('transform', 'translateY(0)');
			}
		);

		// 初始化日期时间选择器
		$(".datetimepicker").each(function(){
			var opt = {
				language: "zh-CN",
				format: "yyyy-mm-dd hh:ii",
				autoclose: true,
				minuteStep: 1,
				todayBtn: true,
				todayHighlight: true
			};
			$(this).datetimepicker(opt);
		});

		// 搜索表单优化
		$('.search-form').on('submit', function() {
			// 移除空值参数
			$(this).find('input, select').each(function() {
				if ($(this).val() === '') {
					$(this).prop('disabled', true);
				}
			});

			// 阻止表单默认提交
			return false;
		});
	});

	// 搜索面板展开/收起功能
	function toggleSearchPanel() {
		var $panel = $('#search-panel');
		var $toggle = $('.userjob-search-toggle');
		var $icon = $toggle.find('i');

		if ($panel.is(':visible')) {
			$panel.slideUp(300);
			$icon.removeClass('fa-filter-circle-xmark').addClass('fa-filter');
			$toggle.find('span').text('筛选条件');
		} else {
			$panel.slideDown(300);
			$icon.removeClass('fa-filter').addClass('fa-filter-circle-xmark');
			$toggle.find('span').text('收起筛选');
		}
	}

	// 更新统计数据
	function updateStatistics() {
		var totalResumes = $('.resume-card').length;
		var activeResumes = 0;
		var needReplyCount = 0;

		$('.resume-card').each(function() {
			var $card = $(this);
			// 统计沟通中的简历（状态为0）
			var statusText = $card.find('.resume-status').text().trim();
			if (statusText === '沟通中') {
				activeResumes++;
			}

			// 统计需要回复的简历
			if ($card.find('.need-reply-indicator').length > 0) {
				needReplyCount++;
			}
		});

		// 更新统计显示
		$('#total-resumes').text(totalResumes);
		$('#active-resumes').text(activeResumes);
		$('#need-reply').text(needReplyCount);

		// 添加动画效果
		$('.userjob-stat-number').each(function(index) {
			var $this = $(this);
			setTimeout(function() {
				$this.addClass('userjob-fade-in');
			}, index * 100);
		});
	}

	// 页面加载完成后初始化
	$(document).ready(function() {

		// 延迟更新统计数据，确保简历卡片已加载
		setTimeout(function() {
			updateStatistics();
		}, 500);

		// 初始化搜索面板状态（默认隐藏）
		var hasSearchParams = window.location.search.length > 1;
		if (hasSearchParams) {
			// 如果有搜索参数，显示搜索面板
			$('#search-panel').show();
			$('.userjob-search-toggle i').removeClass('fa-filter').addClass('fa-filter-circle-xmark');
		} else {
			// 默认隐藏搜索面板
			$('#search-panel').hide();
			$('.userjob-search-toggle i').removeClass('fa-filter-circle-xmark').addClass('fa-filter');
		}

		// 搜索表单增强
		$('.userjob-search-form').on('submit', function() {
			var $form = $(this);
			var $submitBtn = $form.find('button[type="submit"]');
			var originalText = $submitBtn.html();

			// 显示加载状态
			$submitBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 搜索中...');

			// 移除空值参数
			$form.find('input, select').each(function() {
				var $field = $(this);
				if ($field.val() === '' && $field.attr('name') !== 'kw') {
					$field.prop('disabled', true);
				}
			});

			// 模拟搜索延迟（实际提交会跳转页面）
			setTimeout(function() {
				$submitBtn.prop('disabled', false).html(originalText);
			}, 1000);
		});

		// 搜索框实时反馈
		$('input[name="val"]').on('input', function() {
			var $input = $(this);
			var value = $input.val();

			if (value.length > 0) {
				$input.css('border-color', '#667eea');
			} else {
				$input.css('border-color', '#e5e7eb');
			}
		});

		// 键盘快捷键
		$(document).on('keydown', function(e) {
			// Ctrl+F 聚焦搜索框
			if (e.ctrlKey && e.keyCode === 70) {
				e.preventDefault();
				$('input[name="val"]').focus();
			}
			// Ctrl+R 重置搜索
			if (e.ctrlKey && e.keyCode === 82) {
				e.preventDefault();
				window.location.href = "{:U('userjob/index')}";
			}
			// Ctrl+S 展开/收起搜索面板
			if (e.ctrlKey && e.keyCode === 83) {
				e.preventDefault();
				toggleSearchPanel();
			}
		});

		// 添加页面加载动画
		setTimeout(function() {
			$('.userjob-page-header').addClass('userjob-fade-in');
			setTimeout(function() {
				$('.userjob-nav-container').addClass('userjob-fade-in-delay-1');
				setTimeout(function() {
					$('.userjob-search-container').addClass('userjob-fade-in-delay-2');
				}, 100);
			}, 100);
		}, 100);
	});

	// 测试快速回复功能
	function testQuickReplyFunction() {
		console.log('开始测试快速回复功能...');

		$.ajax({
			url: '{:U("userjob/testQuickReply")}',
			type: 'GET',
			dataType: 'json',
			success: function(response) {
				console.log('测试结果:', response);
				if (response.status == 1) {
					alert('快速回复功能测试通过！\n' + JSON.stringify(response.data, null, 2));
				} else {
					alert('快速回复功能测试失败：' + response.info);
				}
			},
			error: function(xhr, status, error) {
				console.error('测试请求失败:', {status: status, error: error, responseText: xhr.responseText});
				alert('测试请求失败：' + error);
			}
		});
	}

	// 消息展开/收起功能
	function toggleMessageContent(msgId) {
		var $content = $('#msg-content-' + msgId);
		var $btn = $content.next().find('.message-expand-btn');

		if ($content.hasClass('collapsed')) {
			$content.removeClass('collapsed');
			$btn.text('收起');
		} else {
			$content.addClass('collapsed');
			$btn.text('展开全文');
		}
	}

	// 重复的函数定义已移到全局作用域

	// 重复的submitQuickReply函数定义已移到全局作用域

	// 操作按钮悬停效果
	$(document).on('mouseenter', '.resume-action-btn', function() {
		$(this).css('transform', 'translateY(-1px)');
		if ($(this).hasClass('resume-btn-primary')) {
			$(this).css('background', '#5a67d8');
			$(this).css('box-shadow', '0 6px 12px rgba(102, 126, 234, 0.4)');
		} else if ($(this).hasClass('resume-btn-info')) {
			$(this).css('background', '#2563eb');
			$(this).css('box-shadow', '0 6px 12px rgba(59, 130, 246, 0.4)');
		} else if ($(this).hasClass('resume-btn-success')) {
			$(this).css('background', '#059669');
			$(this).css('box-shadow', '0 6px 12px rgba(16, 185, 129, 0.4)');
		} else if ($(this).hasClass('resume-btn-warning')) {
			$(this).css('background', '#d97706');
			$(this).css('box-shadow', '0 6px 12px rgba(245, 158, 11, 0.4)');
			} else if ($(this).hasClass('resume-btn-training')) {
				$(this).css('background', '#7c3aed');
				$(this).css('box-shadow', '0 6px 12px rgba(139, 92, 246, 0.4)');
		} else if ($(this).hasClass('resume-btn-secondary')) {
			$(this).css('background', '#4b5563');
			$(this).css('box-shadow', '0 6px 12px rgba(107, 114, 128, 0.4)');
		} else if ($(this).hasClass('resume-btn-danger')) {
			$(this).css('background', '#dc2626');
			$(this).css('box-shadow', '0 6px 12px rgba(239, 68, 68, 0.4)');
		}
	});

	$(document).on('mouseleave', '.resume-action-btn', function() {
		$(this).css('transform', 'translateY(0)');
		if ($(this).hasClass('resume-btn-primary')) {
			$(this).css('background', '#667eea');
			$(this).css('box-shadow', '0 2px 4px rgba(102, 126, 234, 0.3)');
		} else if ($(this).hasClass('resume-btn-info')) {
			$(this).css('background', '#3b82f6');
			$(this).css('box-shadow', '0 2px 4px rgba(59, 130, 246, 0.3)');
		} else if ($(this).hasClass('resume-btn-success')) {
			$(this).css('background', '#10b981');
			$(this).css('box-shadow', '0 2px 4px rgba(16, 185, 129, 0.3)');
		} else if ($(this).hasClass('resume-btn-warning')) {
			$(this).css('background', '#f59e0b');
			$(this).css('box-shadow', '0 2px 4px rgba(245, 158, 11, 0.3)');
		} else if ($(this).hasClass('resume-btn-training')) {
			$(this).css('background', '#8b5cf6');
			$(this).css('box-shadow', '0 2px 4px rgba(139, 92, 246, 0.3)');
		} else if ($(this).hasClass('resume-btn-secondary')) {
			$(this).css('background', '#6b7280');
			$(this).css('box-shadow', '0 2px 4px rgba(107, 114, 128, 0.3)');
		} else if ($(this).hasClass('resume-btn-danger')) {
			$(this).css('background', '#ef4444');
			$(this).css('box-shadow', '0 2px 4px rgba(239, 68, 68, 0.3)');
		}
	});

	// 删除用户简历消息功能
	window.deleteUserJobMessage = function(messageId, userJobId) {
		console.log('准备删除用户简历消息:', messageId, userJobId);

		if (!messageId) {
			layer.msg('消息ID无效', {icon: 2});
			return;
		}

		// 确认删除
		layer.confirm('确定要删除这条消息吗？删除后无法恢复。', {
			icon: 3,
			title: '确认删除',
			btn: ['确定删除', '取消']
		}, function(index) {
			// 确定删除
			layer.close(index);

			// 显示加载状态
			var loadingIndex = layer.load(1, {
				shade: [0.3, '#000']
			});

			// 发送删除请求
			$.ajax({
				url: '{:U("Message/deleteMessage")}',
				type: 'POST',
				data: {
					message_id: messageId
				},
				timeout: 15000,
				success: function(response) {
					console.log('删除响应:', response);

					try {
						// 尝试解析JSON响应
						var result = typeof response === 'string' ? JSON.parse(response) : response;

						if (result.status === 1 || result.success === true) {
							layer.msg('消息删除成功', {icon: 1, time: 1500});

							// 从页面中移除消息元素
							var $messageItem = $('[data-message-id="' + messageId + '"]');
							if ($messageItem.length > 0) {
								$messageItem.fadeOut(300, function() {
									$(this).remove();

									// 检查该简历是否还有其他消息
									var $messagesList = $messageItem.closest('.messages-list');
									if ($messagesList.find('.message-item').length === 0) {
										// 如果没有消息了，显示无消息状态
										$messagesList.replaceWith(
											'<div class="no-recent-message">' +
											'<i class="fa fa-comment-o"></i> 暂无沟通记录<br>' +
											'<button class="quick-reply-btn" onclick="showQuickReplyForm(\'' + userJobId + '\')" style="margin-top: 10px;">' +
											'<i class="fa fa-plus"></i> 开始沟通' +
											'</button>' +
											'</div>'
										);
									}
								});
							}
						} else {
							var errorMsg = result.message || result.msg || result.info || '删除失败，请重试';
							layer.msg(errorMsg, {icon: 2});
						}
					} catch (e) {
						// 如果不是JSON格式，检查响应文本
						var responseText = response.toString();
						if (responseText.includes('成功') || responseText.includes('success')) {
							layer.msg('消息删除成功', {icon: 1, time: 1500});

							// 从页面中移除消息元素
							var $messageItem = $('[data-message-id="' + messageId + '"]');
							if ($messageItem.length > 0) {
								$messageItem.fadeOut(300, function() {
									$(this).remove();
								});
							}
						} else {
							layer.msg('删除失败，请重试', {icon: 2});
						}
					}
				},
				error: function(xhr, status, error) {
					console.error('删除请求失败:', {
						status: status,
						error: error,
						responseText: xhr.responseText
					});

					var errorMsg = '网络错误，请重试';
					if (status === 'timeout') {
						errorMsg = '请求超时，请重试';
					} else if (xhr.status === 404) {
						errorMsg = '删除接口不存在，请联系管理员';
					} else if (xhr.status === 500) {
						errorMsg = '服务器错误，请重试';
					} else if (xhr.status === 403) {
						errorMsg = '没有权限删除此消息';
					}

					layer.msg(errorMsg, {icon: 2});
				},
				complete: function() {
					layer.close(loadingIndex);
				}
			});
		}, function(index) {
			// 取消删除
			layer.close(index);
		});
	};

	// 显示错误提示的统一方法
	function showErrorMsg(message, type = 'error') {
		var icon = type === 'success' ? 1 : (type === 'warning' ? 0 : 2);
		layer.msg(message, {
			icon: icon,
			time: 3000,
			shade: 0.3
		});
	}

	// 显示成功提示的统一方法
	function showSuccessMsg(message) {
		layer.msg(message, {
			icon: 1,
			time: 2000,
			shade: 0.3
		});
	}

	// 显示警告提示的统一方法
	function showWarningMsg(message) {
		layer.msg(message, {
			icon: 0,
			time: 3000,
			shade: 0.3
		});
	}

	// 报名培训功能
	function baoming(jobId) {
		if (!jobId) {
			showErrorMsg('简历ID不存在，请刷新页面后重试');
			return;
		}

		// 检查学员是否已报名
		var trainingOrderList = {:json_encode($trainingOrderList)};

		// 使用 some 方法进行比较，确保类型一致
		var isAlreadyRegistered = false;
		if (trainingOrderList && Object.keys(trainingOrderList).length > 0) {
			isAlreadyRegistered = Object.keys(trainingOrderList).some(function(id) {
				return String(id) === String(jobId);
			});
		}

		if (isAlreadyRegistered) {
			showWarningMsg('该学员已报名培训，请勿重复报名');
			return;
		}

		// 显示加载提示
		var loadingIndex = layer.load(1, {
			shade: [0.3, '#000']
		});

		// 获取学员简历信息
		$.ajax({
			url: "{:U('training/getUserJobInfo')}",
			type: 'GET',
			data: {user_job_id: jobId},
			dataType: 'json',
			timeout: 10000, // 10秒超时
			success: function(res) {
				layer.close(loadingIndex);
				if (res.status == 1) {
					// 继续创建培训订单表单
					createTrainingOrderForm(jobId);
				} else {
					showErrorMsg(res.msg || '获取学员信息失败，请稍后重试');
				}
			},
			error: function(xhr, status, error) {
				layer.close(loadingIndex);
				if (status === 'timeout') {
					showErrorMsg('请求超时，请检查网络连接后重试');
				} else {
					showErrorMsg('网络错误，请检查网络连接后重试');
				}
			}
		});
	}

	// 创建培训订单表单
	function createTrainingOrderForm(jobId) {
		// 显示加载提示
		var formLoadingIndex = layer.load(1, {
			shade: [0.3, '#000']
		});

		// 先通过AJAX获取表单数据
		$.ajax({
			url: "{:U('training/getFormData')}",
			type: 'GET',
			dataType: 'json',
			timeout: 10000,
			success: function(res) {
				layer.close(formLoadingIndex);
				if (res.status == 1) {
					// 创建弹窗内容
					var modalContent = '<div class="training-form" style="padding: 20px; background-color: #fff;">';
					modalContent += '<form id="trainingForm">';

					// 隐藏字段 - 简历ID
					modalContent += '<input type="hidden" id="modal_job_id" value="' + jobId + '">';

					// 学员信息 - 只读，因为是从简历中获取的
					modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-user" style="margin-right: 5px;"></i>学员：</label>';
					modalContent += '<select name="user_id" id="modal_user_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required disabled>';

					// 根据jobId过滤出对应的学员
					var selectedUser = null;
					var selectedResumeType = 1; // 默认为自有简历
					var selectedServiceStationId = '';

					for (var i = 0; i < res.data.users.length; i++) {
						if (res.data.users[i].id == jobId) {
							selectedUser = res.data.users[i];
							selectedResumeType = res.data.users[i].resume_type || 1;
							selectedServiceStationId = res.data.users[i].service_station_id || '';

							var resumeTypeText = res.data.users[i].resume_type_text ? ' [' + res.data.users[i].resume_type_text + ']' : '';
							modalContent += '<option value="' + res.data.users[i].id + '" selected data-resume-type="' + selectedResumeType + '" data-service-station-id="' + selectedServiceStationId + '">' +
											res.data.users[i].realname + ' (' + res.data.users[i].mobile + ')' + resumeTypeText +
											'</option>';
							break;
						}
					}

					// 如果没有找到对应的学员，显示提示信息并继续创建表单（后端会自动创建用户）
					if (!selectedUser) {
						modalContent += '<option value="' + jobId + '" selected data-resume-type="1" data-service-station-id="">系统将自动创建用户记录</option>';
					}

					modalContent += '</select>';
					modalContent += '</div>';

					// 培训项目
					modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-project-diagram" style="margin-right: 5px;"></i>培训项目：</label>';
					modalContent += '<select name="project_id" id="modal_project_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
					modalContent += '<option value="">请选择培训项目</option>';

					// 添加项目选项
					for (var i = 0; i < res.data.projects.length; i++) {
						modalContent += '<option value="' + res.data.projects[i].id + '">' +
										res.data.projects[i].name +
										'</option>';
					}

					modalContent += '</select>';
					modalContent += '</div>';

					// 培训岗位
					modalContent += '<div class="form-item" style="margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-tag" style="margin-right: 5px;"></i>培训岗位：</label>';
					modalContent += '<select name="post_id" id="modal_post_id" class="form-select" style="width: 100%; height: 44px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 15px; color: #333; box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);" required>';
					modalContent += '<option value="">请先选择培训项目</option>';
					modalContent += '</select>';
					modalContent += '</div>';

					// 招就办价格显示区域
					modalContent += '<div class="form-item zsb-price-section" id="zsb_fee_display_section" style="display: none; margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-check" style="margin-right: 5px;"></i>报名费：</label>';
					modalContent += '<div class="zsb-fee-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
					modalContent += '<span id="zsb_fee_amount">请先选择培训岗位</span>';
					modalContent += '</div>';
					modalContent += '<div style="font-size: 12px; color: #666; margin-top: 5px;">报名费由招就办价格配置自动确定</div>';
					modalContent += '</div>';

					// 自有简历报名费输入区域
					modalContent += '<div class="form-item own-resume-section" id="own_fee_input_section" style="margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-money" style="margin-right: 5px;"></i>报名费：</label>';
					modalContent += '<input type="text" id="modal_fee_input" class="form-control" placeholder="请输入报名费（元）" style="width: 100%; height: 40px; border: 1px solid #ddd; border-radius: 5px; padding: 0 12px; background-color: #fff; font-size: 14px; color: #333;" required>';
					modalContent += '<div id="fee_range_hint" style="font-size: 12px; color: #666; margin-top: 5px;">请先选择培训岗位</div>';
					modalContent += '</div>';

					// 招就办收益显示区域
					modalContent += '<div class="form-item zsb-price-section" id="modal_zsb_profit" style="display: none; margin-bottom: 20px;">';
					modalContent += '<label style="display: block; margin-bottom: 8px; font-weight: bold; color: #333; font-size: 15px;"><i class="fa fa-check" style="margin-right: 5px;"></i>招就办收益：</label>';
					modalContent += '<div class="zsb-commission-display" style="padding: 12px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; font-weight: bold; color: #07c160;">';
					modalContent += '<span id="zsb_commission_amount">0.00 元</span>';
					modalContent += '</div>';
					modalContent += '</div>';

					modalContent += '</form>';
					modalContent += '</div>';

					// 打开弹窗
					layer.open({
						type: 1,
						title: '<span style="font-weight: bold; font-size: 16px;">创建培训订单</span>',
						area: ['480px', 'auto'],
						maxHeight: '80vh',
						skin: 'layui-layer-molv',
						content: modalContent,
						btn: ['提交', '取消'],
						btnAlign: 'c',
						success: function(layero) {
							// 添加样式到弹窗
							$(layero).find('.layui-layer-title').css({
								'background-color': '#07c160',
								'color': '#fff',
								'border': 'none',
								'height': '50px',
								'line-height': '50px'
							});

							$(layero).find('.layui-layer-btn a').css({
								'border-radius': '4px',
								'height': '38px',
								'line-height': '38px',
								'padding': '0 18px',
								'font-size': '15px'
							});

							$(layero).find('.layui-layer-btn .layui-layer-btn0').css({
								'border-color': '#07c160',
								'background-color': '#07c160',
								'color': '#fff'
							});

							// 绑定项目选择事件
							$('#modal_project_id').on('change', function() {
								var projectId = $(this).val();
								if (projectId) {
									loadProjectPosts(projectId);
								} else {
									$('#modal_post_id').html('<option value="">请先选择培训项目</option>');
								}
							});

							// 绑定岗位选择事件
							$('#modal_post_id').on('change', function() {
								var postId = $(this).val();
								if (postId) {
									loadPostPriceInfo(postId);
								} else {
									// 没有选择岗位时，清空所有显示
									$('#zsb_fee_amount').text('请先选择培训岗位');
									$('#zsb_fee_display_section').hide();
									$('#modal_zsb_profit').hide();
									$('#modal_fee_input').val('');
									$('#fee_range_hint').text('请先选择培训岗位');
									$('#own_fee_input_section').show();
								}
							});
						},
						yes: function(index, layero) {
							// 提交表单
							submitTrainingForm(index);
						},
						btn2: function(index, layero) {
							// 取消
							layer.close(index);
						}
					});
				} else {
					showErrorMsg(res.msg || '获取表单数据失败，请稍后重试');
				}
			},
			error: function(xhr, status, error) {
				layer.close(formLoadingIndex);
				if (status === 'timeout') {
					showErrorMsg('请求超时，请检查网络连接后重试');
				} else {
					showErrorMsg('网络错误，请检查网络连接后重试');
				}
			}
		});
	}

	// 加载项目岗位
	function loadProjectPosts(projectId) {
		if (!projectId) {
			showWarningMsg('请先选择培训项目');
			return;
		}

		$.ajax({
			url: "{:U('training/getProjectPosts')}",
			type: 'GET',
			data: {project_id: projectId},
			dataType: 'json',
			timeout: 8000,
			success: function(res) {
				if (res.status == 1) {
					if (res.data && res.data.length > 0) {
						var options = '<option value="">请选择培训岗位</option>';
						for (var i = 0; i < res.data.length; i++) {
							options += '<option value="' + res.data[i].id + '">' + res.data[i].job_name + '</option>';
						}
						$('#modal_post_id').html(options);
					} else {
						$('#modal_post_id').html('<option value="">该项目暂无可用岗位</option>');
						showWarningMsg('该项目暂无可用岗位');
					}
				} else {
					showErrorMsg(res.msg || '获取岗位列表失败，请稍后重试');
					$('#modal_post_id').html('<option value="">获取岗位失败</option>');
				}
			},
			error: function(xhr, status, error) {
				if (status === 'timeout') {
					showErrorMsg('请求超时，请检查网络连接后重试');
				} else {
					showErrorMsg('网络错误，请检查网络连接后重试');
				}
				$('#modal_post_id').html('<option value="">网络错误</option>');
			}
		});
	}

	// 加载岗位价格信息
	function loadPostPriceInfo(postId) {
		// 获取当前选择的学员信息
		var selectedOption = $('#modal_user_id option:selected');
		var selectedResumeType = selectedOption.data('resume-type') || 1;
		var selectedServiceStationId = selectedOption.data('service-station-id') || '';

		console.log('岗位选择变化，岗位ID:', postId, '简历类型:', selectedResumeType, '服务站ID:', selectedServiceStationId);

		if (selectedResumeType == 2) {
			// 招就办简历：获取招就办价格配置
			console.log('招就办简历调用价格API，岗位ID:', postId, '招就办ID:', selectedServiceStationId);
			$.ajax({
				url: "{:U('training/getZsbPostPrice')}",
				type: 'GET',
				data: {
					post_id: postId,
					zsb_id: selectedServiceStationId // 传递招就办ID
				},
				dataType: 'json',
				success: function(res) {
					console.log('招就办价格API调用成功:', res);
					if (res.status == 1) {
						var data = res.data;

						// 显示招就办费用
						$('#zsb_fee_amount').text(data.sale_price_formatted + ' 元');
						$('#zsb_fee_display_section').show();

						// 设置隐藏输入框的值（用于提交）
						$('#modal_fee_input').val(data.sale_price_formatted);

						// 显示招就办收益
						$('#zsb_commission_amount').text(data.commission_formatted + ' 元');
						$('#modal_zsb_profit').show();

						// 隐藏自有简历的输入区域
						$('#own_fee_input_section').hide();

					} else {
						$('#zsb_fee_amount').text('未配置价格');
						$('#zsb_fee_display_section').show();
						$('#modal_zsb_profit').hide();
						$('#own_fee_input_section').hide();
					}
				},
				error: function(xhr, status, error) {
					console.error('获取招就办价格失败:', error);
					console.error('响应状态:', xhr.status);
					console.error('响应内容:', xhr.responseText);
					$('#zsb_fee_amount').text('获取价格失败');
					$('#zsb_fee_display_section').show();
					$('#modal_zsb_profit').hide();
					$('#own_fee_input_section').hide();
				}
			});
		} else {
			// 自有简历：显示输入框，隐藏招就办价格区域
			$('#zsb_fee_display_section').hide();
			$('#modal_zsb_profit').hide();
			$('#own_fee_input_section').show();
			$('#fee_range_hint').text('请输入报名费（元）');
		}
	}

	// 提交培训表单
	function submitTrainingForm(layerIndex) {
		var jobId = $('#modal_job_id').val();
		var postId = $('#modal_post_id').val();
		var feeAmount = $('#modal_fee_input').val();

		// 获取当前选择的学员信息
		var selectedOption = $('#modal_user_id option:selected');
		var selectedResumeType = selectedOption.data('resume-type') || 1;

		// 表单验证
		if (!jobId) {
			showErrorMsg('学员信息异常，请刷新页面后重试');
			return;
		}

		if (!postId) {
			showWarningMsg('请选择培训岗位');
			return;
		}

		// 根据简历类型验证报名费
		if (selectedResumeType == 2) {
			// 招就办简历：检查是否显示了价格配置
			if ($('#zsb_fee_display_section').is(':visible')) {
				var zsbFeeText = $('#zsb_fee_amount').text();
				if (zsbFeeText.includes('未配置价格') || zsbFeeText.includes('获取价格失败')) {
					showErrorMsg('该岗位未配置招就办价格，无法报名');
					return;
				}
			} else {
				showWarningMsg('请先选择培训岗位以获取价格信息');
				return;
			}
		} else {
			// 自有简历：验证输入的报名费
			if (!feeAmount || isNaN(feeAmount) || parseFloat(feeAmount) <= 0) {
				showWarningMsg('请输入有效的报名费（大于0的数字）');
				return;
			}
		}

		// 显示提交中的loading
		var submitLoadingIndex = layer.load(2, {
			shade: [0.3, '#000']
		});

		// 提交表单
		$.ajax({
			url: "{:U('training/create')}",
			type: 'POST',
			data: {
				user_id: jobId,
				post_id: postId,
				fee_amount: feeAmount
			},
			dataType: 'json',
			timeout: 15000, // 15秒超时
			success: function(res) {
				layer.close(submitLoadingIndex);
				if (res.status == 1) {
					layer.close(layerIndex);
					showSuccessMsg('培训报名成功！');
					setTimeout(function() {
						window.location.reload();
					}, 1500);
				} else {
					// 根据不同的错误类型显示不同的提示
					var errorMsg = res.info || '培训报名失败';
					if (errorMsg.includes('已报名') || errorMsg.includes('重复')) {
						showWarningMsg(errorMsg);
					} else if (errorMsg.includes('网络') || errorMsg.includes('连接')) {
						showErrorMsg('网络连接异常，请检查网络后重试');
					} else {
						showErrorMsg(errorMsg + '，请稍后重试');
					}
				}
			},
			error: function(xhr, status, error) {
				layer.close(submitLoadingIndex);
				if (status === 'timeout') {
					showErrorMsg('提交超时，请检查网络连接后重试');
				} else if (xhr.status === 500) {
					showErrorMsg('服务器内部错误，请联系管理员');
				} else if (xhr.status === 404) {
					showErrorMsg('请求地址不存在，请刷新页面后重试');
				} else {
					showErrorMsg('网络错误，请检查网络连接后重试');
				}
			}
		});
	}
</script>
