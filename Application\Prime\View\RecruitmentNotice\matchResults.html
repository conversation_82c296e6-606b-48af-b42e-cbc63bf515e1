<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="recruitment-page-wrapper">
                <div class="recruitment-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="recruitment-page-header">
                        <div class="recruitment-header-content">
                            <div class="recruitment-page-title">
                                <div class="recruitment-title-icon">
                                    <i class="fa fa-list-alt"></i>
                                </div>
                                <div class="recruitment-title-text">
                                    <h1 class="recruitment-title-main">简历匹配结果</h1>
                                    <p class="recruitment-title-sub">智能匹配分析报告</p>
                                </div>
                            </div>
                            <div class="recruitment-header-actions">
                                <if condition="$noticeId">
                                    <a href="{:U('executeMatch', ['notice_id' => $noticeId])}" class="recruitment-header-btn btn-success">
                                        <i class="fa fa-refresh"></i>
                                        <span>重新匹配</span>
                                    </a>
                                </if>
                                <a href="{:U('index')}" class="recruitment-header-btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i>
                                    <span>返回列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <if condition="!empty($stats)">
                        <div class="recruitment-stats-container">
                            <div class="recruitment-stat-card">
                                <div class="stat-icon">
                                    <i class="fa fa-users"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">{$stats.total_matches}</div>
                                    <div class="stat-label">总匹配数</div>
                                </div>
                            </div>
                            <div class="recruitment-stat-card stat-success">
                                <div class="stat-icon">
                                    <i class="fa fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">{$stats.qualified_matches}</div>
                                    <div class="stat-label">符合要求</div>
                                </div>
                            </div>
                            <div class="recruitment-stat-card stat-info">
                                <div class="stat-icon">
                                    <i class="fa fa-chart-line"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">{:round($stats['avg_score'], 1)}</div>
                                    <div class="stat-label">平均分数</div>
                                </div>
                            </div>
                            <div class="recruitment-stat-card stat-warning">
                                <div class="stat-icon">
                                    <i class="fa fa-trophy"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">{$stats.max_score}</div>
                                    <div class="stat-label">最高分数</div>
                                </div>
                            </div>
                        </div>
                    </if>

                    <!-- 搜索表单 -->
                    <div class="recruitment-search-container">
                        <div class="search-form-header">
                            <div class="search-title">
                                <i class="fa fa-filter"></i>
                                <span>筛选条件</span>
                            </div>
                        </div>
                        <form class="recruitment-search-form" method="get">
                            <div class="search-row">
                                <div class="search-group">
                                    <label class="search-label">
                                        <i class="fa fa-bullhorn"></i>
                                        招聘公告
                                    </label>
                                    <select name="notice_id" id="notice_id" class="form-control">
                                        <option value="">选择招聘公告</option>
                                        <volist name="notices" id="notice" key="nid">
                                            <option value="{$nid}" <if condition="$noticeId eq $nid">selected</if>>{$notice}</option>
                                        </volist>
                                    </select>
                                </div>
                                <if condition="!empty($posts)">
                                    <div class="search-group">
                                        <label class="search-label">
                                            <i class="fa fa-briefcase"></i>
                                            岗位
                                        </label>
                                        <select name="post_id" id="post_id" class="form-control">
                                            <option value="">选择岗位</option>
                                            <volist name="posts" id="post">
                                                <option value="{$post.id}" <if condition="$postId eq $post.id">selected</if>>{$post.job_name}</option>
                                            </volist>
                                        </select>
                                    </div>
                                </if>
                                <div class="search-group">
                                    <label class="search-label">
                                        <i class="fa fa-check-circle"></i>
                                        匹配结果
                                    </label>
                                    <select name="is_qualified" id="is_qualified" class="form-control">
                                        <option value="">全部结果</option>
                                        <option value="1" <if condition="$isQualified eq '1'">selected</if>>符合要求</option>
                                        <option value="0" <if condition="$isQualified eq '0'">selected</if>>不符合要求</option>
                                    </select>
                                </div>
                                <div class="search-group">
                                    <label class="search-label">
                                        <i class="fa fa-star"></i>
                                        最低分数
                                    </label>
                                    <input type="number" class="form-control" id="min_score" name="min_score" value="{$minScore}"
                                           placeholder="输入最低分数" min="0" max="100">
                                </div>
                            </div>
                            <div class="search-actions">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fa fa-search"></i>
                                    <span>搜索</span>
                                </button>
                                <a href="{:U('matchResults')}" class="btn btn-secondary btn-lg">
                                    <i class="fa fa-refresh"></i>
                                    <span>重置</span>
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                    <!-- 匹配结果卡片 -->
                    <div class="recruitment-results-container">
                        <if condition="!empty($list)">
                            <div class="results-grid">
                                <volist name="list" id="vo">
                                    <div class="match-result-card">
                                        <div class="result-card-header">
                                            <div class="candidate-info">
                                                <div class="candidate-avatar">
                                                    <i class="fa fa-user"></i>
                                                </div>
                                                <div class="candidate-details">
                                                    <div class="candidate-name">{$vo.name}</div>
                                                    <div class="candidate-meta">
                                                        <span class="meta-item">
                                                            <i class="fa fa-venus-mars"></i>
                                                            {$vo.gender}
                                                        </span>
                                                        <span class="meta-item">
                                                            <i class="fa fa-phone"></i>
                                                            {$vo.phone}
                                                        </span>
                                                    </div>
                                                    <div class="candidate-education">
                                                        <span class="education-item">
                                                            <i class="fa fa-graduation-cap"></i>
                                                            {$vo.education_level}
                                                        </span>
                                                        <span class="education-item">
                                                            <i class="fa fa-book"></i>
                                                            {$vo.major}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="match-score-container">
                                                <?php if($vo['match_score'] >= 80): ?>
                                                    <div class="match-score score-excellent">
                                                        <div class="score-number">{$vo.match_score}</div>
                                                        <div class="score-label">优秀</div>
                                                    </div>
                                                <?php elseif($vo['match_score'] >= 60): ?>
                                                    <div class="match-score score-good">
                                                        <div class="score-number">{$vo.match_score}</div>
                                                        <div class="score-label">良好</div>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="match-score score-poor">
                                                        <div class="score-number">{$vo.match_score}</div>
                                                        <div class="score-label">较差</div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <div class="result-card-body">
                                            <div class="job-info">
                                                <div class="job-title">
                                                    <i class="fa fa-briefcase"></i>
                                                    <span>{$vo.job_name}</span>
                                                </div>
                                                <div class="notice-title">
                                                    <i class="fa fa-bullhorn"></i>
                                                    <span>{$vo.notice_title}</span>
                                                </div>
                                            </div>

                                            <div class="qualification-status">
                                                <if condition="$vo.is_qualified eq 1">
                                                    <div class="status-badge status-qualified">
                                                        <i class="fa fa-check-circle"></i>
                                                        <span>符合要求</span>
                                                    </div>
                                                <else />
                                                    <div class="status-badge status-unqualified">
                                                        <i class="fa fa-times-circle"></i>
                                                        <span>不符合要求</span>
                                                    </div>
                                                </if>
                                            </div>
                                        </div>

                                        <div class="result-card-actions">
                                            <button type="button" class="action-btn btn-details"
                                                    onclick="showMatchDetails({$vo.user_job_id}, '{$vo.name}')">
                                                <i class="fa fa-eye"></i>
                                                <span>查看详情</span>
                                            </button>
                                            <a href="{:U('Prime/Userjob/joballinfo', ['id' => $vo['user_job_id']])}"
                                               class="action-btn btn-resume" target="_blank">
                                                <i class="fa fa-user"></i>
                                                <span>查看简历</span>
                                            </a>
                                        </div>
                                    </div>
                                </volist>
                            </div>
                        <else />
                            <div class="empty-results">
                                <div class="empty-icon">
                                    <i class="fa fa-search"></i>
                                </div>
                                <div class="empty-text">
                                    <h3>暂无匹配结果</h3>
                                    <p>请调整筛选条件或重新执行匹配</p>
                                </div>
                            </div>
                        </if>
                    </div>

                    <!-- 数据统计信息 -->
                    <div class="recruitment-stats-bar">
                        <div class="stats-info">
                            <i class="fa fa-info-circle"></i>
                            <span>共找到 <strong>{$count}</strong> 条匹配结果</span>
                            <if condition="$totalPages gt 1">
                                <span>，第 <strong>{$page}</strong> / <strong>{$totalPages}</strong> 页</span>
                            </if>
                        </div>
                    </div>

                    <!-- 现代化分页 -->
                    <if condition="$totalPages gt 1">
                        <div class="recruitment-pagination-container">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="dataTables_info">
                                        显示第 <strong>{$startRecord}</strong> - <strong>{$endRecord}</strong> 条记录
                                        <br>共 <strong>{$count}</strong> 条记录
                                    </div>
                                </div>
                                <div class="col-sm-4 text-center">
                                    <div class="pagination-jump">
                                        <div class="input-group">
                                            <span class="input-group-addon">跳转到</span>
                                            <input type="number" class="form-control text-center" id="jumpToPage"
                                                   min="1" max="{$totalPages}" value="{$page}">
                                            <span class="input-group-btn">
                                                <button class="btn btn-primary" type="button" onclick="jumpToPage()">
                                                    <i class="fa fa-arrow-right"></i>
                                                </button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="dataTables_paginate paging_simple_numbers pull-right">
                                        <ul class="pagination">
                                            <!-- 首页 -->
                                            <if condition="$page gt 1">
                                                <li>
                                                    <a href="{:U('matchResults', array_merge($_GET, ['p' => 1]))}"
                                                       title="首页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-double-left"></i>
                                                    </a>
                                                </li>
                                            </if>

                                            <!-- 上一页 -->
                                            <if condition="$page gt 1">
                                                <li>
                                                    <a href="{:U('matchResults', array_merge($_GET, ['p' => $page-1]))}"
                                                       title="上一页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-left"></i>
                                                    </a>
                                                </li>
                                            <else />
                                                <li class="disabled">
                                                    <span><i class="fa fa-angle-left"></i></span>
                                                </li>
                                            </if>

                                            <!-- 页码 -->
                                            <php>
                                                $start = max(1, $page - 2);
                                                $end = min($totalPages, $page + 2);

                                                // 如果总页数较少，显示所有页码
                                                if ($totalPages <= 7) {
                                                    $start = 1;
                                                    $end = $totalPages;
                                                }
                                            </php>

                                            <!-- 显示省略号（前） -->
                                            <if condition="$start gt 1">
                                                <li class="disabled"><span>...</span></li>
                                            </if>

                                            <for start="start" end="end">
                                                <li <if condition="$i eq $page">class="active"</if>>
                                                    <a href="{:U('matchResults', array_merge($_GET, ['p' => $i]))}">{$i}</a>
                                                </li>
                                            </for>

                                            <!-- 显示省略号（后） -->
                                            <if condition="$end lt $totalPages">
                                                <li class="disabled"><span>...</span></li>
                                            </if>

                                            <!-- 下一页 -->
                                            <if condition="$page lt $totalPages">
                                                <li>
                                                    <a href="{:U('matchResults', array_merge($_GET, ['p' => $page+1]))}"
                                                       title="下一页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-right"></i>
                                                    </a>
                                                </li>
                                            <else />
                                                <li class="disabled">
                                                    <span><i class="fa fa-angle-right"></i></span>
                                                </li>
                                            </if>

                                            <!-- 末页 -->
                                            <if condition="$page lt $totalPages">
                                                <li>
                                                    <a href="{:U('matchResults', array_merge($_GET, ['p' => $totalPages]))}"
                                                       title="末页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-double-right"></i>
                                                    </a>
                                                </li>
                                            </if>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 匹配详情模态框 -->
<div class="modal fade" id="matchDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">匹配详情</h4>
            </div>
            <div class="modal-body" id="matchDetailsContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 确保Bootstrap JavaScript已加载 -->
<script src="__ROOT__/static/js/lib/bootstrap.min.js"></script>

<script>
// 页面匹配详情数据
var matchDetailsData = {};
<volist name="list" id="vo">
matchDetailsData[{$vo.user_job_id}] = {
    match_details: <if condition="!empty($vo.match_details)"><php>echo json_encode($vo['match_details'], JSON_UNESCAPED_UNICODE);</php><else />{}</if>,
    match_score: <if condition="$vo.match_score">{$vo.match_score}<else />0</if>,
    is_qualified: <if condition="$vo.is_qualified">{$vo.is_qualified}<else />0</if>
};
</volist>

// 显示匹配详情
function showMatchDetails(userJobId, userName) {
    console.log('showMatchDetails called:', userJobId, userName);

    // 检查jQuery和Bootstrap是否加载
    if (typeof $ === 'undefined') {
        alert('jQuery未加载');
        return;
    }

    if (typeof $.fn.modal === 'undefined') {
        alert('Bootstrap模态框功能未加载');
        return;
    }

    $('#matchDetailsModal .modal-title').html('<i class="fa fa-user"></i> ' + userName + ' - 匹配详情');
    $('#matchDetailsContent').html('<div class="text-center" style="padding: 30px;"><i class="fa fa-spinner fa-spin fa-2x"></i><br><br>加载中...</div>');
    $('#matchDetailsModal').modal('show');

    // 获取匹配详情数据
    setTimeout(function() {
        var userData = matchDetailsData[userJobId];
        if (!userData) {
            $('#matchDetailsContent').html('<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> 未找到匹配数据</div>');
            return;
        }

        var html = '';

        // 总体匹配情况
        html += '<div class="row" style="margin-bottom: 20px;">';
        html += '<div class="col-md-12">';
        html += '<div class="panel panel-primary">';
        html += '<div class="panel-heading"><i class="fa fa-chart-line"></i> 总体匹配情况</div>';
        html += '<div class="panel-body text-center">';
        html += '<div class="row">';
        html += '<div class="col-md-4">';
        html += '<h3 class="text-primary">' + userData.match_score + '分</h3>';
        html += '<p class="text-muted">匹配分数</p>';
        html += '</div>';
        html += '<div class="col-md-4">';
        var qualifiedText = userData.is_qualified == 1 ? '<span class="text-success">符合要求</span>' : '<span class="text-danger">不符合要求</span>';
        html += '<h3>' + qualifiedText + '</h3>';
        html += '<p class="text-muted">匹配结果</p>';
        html += '</div>';
        html += '<div class="col-md-4">';
        var scoreClass = userData.match_score >= 80 ? 'success' : (userData.match_score >= 60 ? 'warning' : 'danger');
        html += '<span class="label label-' + scoreClass + ' label-lg" style="font-size: 14px; padding: 8px 12px;">等级评定</span>';
        html += '<p class="text-muted" style="margin-top: 10px;">匹配等级</p>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';

        // 详细匹配项
        if (userData.match_details && typeof userData.match_details === 'object' && Object.keys(userData.match_details).length > 0) {
            html += '<div class="row">';
            html += '<div class="col-md-12">';
            html += '<div class="panel panel-default">';
            html += '<div class="panel-heading"><i class="fa fa-list-alt"></i> 详细匹配项</div>';
            html += '<div class="panel-body">';
            html += '<div class="row">';

            for (var key in userData.match_details) {
                var detail = userData.match_details[key];
                if (detail && typeof detail === 'object') {
                    var statusClass = detail.status == 'pass' ? 'success' : 'danger';
                    var statusIcon = detail.status == 'pass' ? 'check-circle' : 'times-circle';
                    var statusText = detail.status == 'pass' ? '通过' : '不通过';

                    html += '<div class="col-md-6" style="margin-bottom: 15px;">';
                    html += '<div class="alert alert-' + statusClass + '" style="margin-bottom: 0;">';
                    html += '<div class="row">';
                    html += '<div class="col-xs-8">';
                    html += '<strong><i class="fa fa-' + statusIcon + '"></i> ' + getFieldName(key) + '</strong>';
                    html += '<br><small>' + (detail.message || '无详细信息') + '</small>';
                    html += '</div>';
                    html += '<div class="col-xs-4 text-right">';
                    html += '<span class="badge badge-' + statusClass + '">' + statusText + '</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                }
            }

            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
            html += '</div>';
        } else {
            html += '<div class="alert alert-info">';
            html += '<i class="fa fa-info-circle"></i> 暂无详细匹配项数据';
            html += '</div>';
        }

        $('#matchDetailsContent').html(html);
    }, 300);
}

// 获取字段中文名称
function getFieldName(key) {
    var fieldNames = {
        'age': '年龄要求',
        'gender': '性别要求',
        'height': '身高要求',
        'education': '学历要求',
        'education_level': '学历水平',
        'major': '专业要求',
        'work_experience': '工作经验',
        'experience': '经验要求',
        'skills': '技能要求',
        'location': '地区要求',
        'salary': '薪资要求',
        'other': '其他要求'
    };
    return fieldNames[key] || key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// 跳转到指定页面
function jumpToPage() {
    var page = parseInt($('#jumpToPage').val());
    var totalPages = {$totalPages};

    if (isNaN(page) || page < 1 || page > totalPages) {
        alert('请输入有效的页码（1-' + totalPages + '）');
        $('#jumpToPage').focus();
        return;
    }

    var currentUrl = window.location.href;
    var url = new URL(currentUrl);
    url.searchParams.set('p', page);
    window.location.href = url.toString();
}

// 回车键跳转
function handleJumpKeyPress(event) {
    if (event.keyCode === 13) {
        jumpToPage();
    }
}

$(function() {
    // 招聘公告选择变化时，重新加载岗位列表
    $('select[name="notice_id"]').on('change', function() {
        var noticeId = $(this).val();
        if (noticeId) {
            window.location.href = '{:U("matchResults")}?notice_id=' + noticeId;
        }
    });

    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();

    // 跳转页面输入框回车事件
    $('#jumpToPage').on('keypress', handleJumpKeyPress);

    // 页面大小选择变化
    $('#pageSize').on('change', function() {
        var pageSize = $(this).val();
        var currentUrl = window.location.href;
        var url = new URL(currentUrl);
        url.searchParams.set('pageSize', pageSize);
        url.searchParams.set('p', 1); // 重置到第一页
        window.location.href = url.toString();
    });
});
</script>

<style>
/* 匹配结果页面样式 */
.recruitment-results-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
    max-width: 1600px;
    margin-left: auto;
    margin-right: auto;
}

.results-grid {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 1.5rem;
}

/* 匹配结果卡片 */
.match-result-card {
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.match-result-card:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.result-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.candidate-info {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.candidate-avatar {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.candidate-details {
    flex: 1;
}

.candidate-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.candidate-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    opacity: 0.9;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.candidate-education {
    display: flex;
    gap: 1rem;
    font-size: 1rem;
    opacity: 0.9;
}

.education-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* 匹配分数 */
.match-score-container {
    flex-shrink: 0;
}

.match-score {
    text-align: center;
    padding: 0.75rem;
    border-radius: 0.5rem;
    min-width: 4rem;
}

.score-excellent {
    background: rgba(16, 185, 129, 0.2);
    border: 2px solid rgba(16, 185, 129, 0.3);
}

.score-good {
    background: rgba(245, 158, 11, 0.2);
    border: 2px solid rgba(245, 158, 11, 0.3);
}

.score-poor {
    background: rgba(239, 68, 68, 0.2);
    border: 2px solid rgba(239, 68, 68, 0.3);
}

.score-number {
    font-size: 1.7rem;
    font-weight: 700;
    line-height: 1;
}

.score-label {
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.25rem;
    opacity: 0.8;
}

.result-card-body {
    padding: 1.5rem;
}

.job-info {
    margin-bottom: 1rem;
}

.job-title, .notice-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 1.15rem;
    color: #374151;
}

.job-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.job-title i {
    color: #667eea;
}

.notice-title {
    font-size: 0.875rem;
    color: #6b7280;
}

.notice-title i {
    color: #9ca3af;
}

.qualification-status {
    display: flex;
    justify-content: center;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-qualified {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-unqualified {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.result-card-actions {
    padding: 1rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 0.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-details {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.btn-details:hover {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(6, 182, 212, 0.4);
    color: white;
    text-decoration: none;
}

.btn-resume {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-resume:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

/* 空结果样式 */
.empty-results {
    padding: 4rem 2rem;
    text-align: center;
    color: #6b7280;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.empty-text h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #374151;
}

.empty-text p {
    font-size: 1rem;
    margin: 0;
}

/* 搜索表单样式 */
.recruitment-search-container {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 2rem;
    overflow: hidden;
}

.search-form-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem 2rem;
    border-bottom: 2px solid #e2e8f0;
}

.search-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
}

.search-title i {
    color: #667eea;
    font-size: 1.4rem;
}

.recruitment-search-form {
    padding: 2rem;
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.search-group {
    display: flex;
    flex-direction: column;
}

.search-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.search-label i {
    color: #667eea;
    width: 1rem;
}

.search-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 2px solid #e2e8f0;
}

/* 匹配详情模态框样式优化 */
#matchDetailsModal .modal-dialog {
    width: 90%;
    max-width: 900px;
}

#matchDetailsModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom: none;
}

#matchDetailsModal .modal-header .close {
    color: white;
    opacity: 0.8;
    font-size: 24px;
}

#matchDetailsModal .modal-header .close:hover {
    opacity: 1;
}

#matchDetailsModal .modal-body {
    padding: 20px;
    background-color: #f8f9fa;
}

#matchDetailsModal .panel {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

#matchDetailsModal .panel-primary > .panel-heading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

#matchDetailsModal .panel-default > .panel-heading {
    background-color: #fff;
    border-bottom: 2px solid #667eea;
    color: #333;
    font-weight: 600;
}

#matchDetailsModal .alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

#matchDetailsModal .alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

#matchDetailsModal .alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

#matchDetailsModal .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

#matchDetailsModal .badge {
    font-size: 11px;
    padding: 4px 8px;
}

#matchDetailsModal .label-lg {
    font-size: 16px !important;
    padding: 10px 15px !important;
    border-radius: 20px !important;
}

/* 分页样式优化 */
.panel-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 2px solid #667eea;
    padding: 15px 20px;
}

.dataTables_info {
    margin-top: 8px;
    color: #495057;
    font-size: 14px;
}

.dataTables_info strong {
    font-weight: 600;
}

.pagination-jump {
    margin-top: 5px;
}

.pagination-jump .input-group-addon {
    background: #667eea;
    color: white;
    border-color: #667eea;
    font-size: 12px;
}

.pagination-jump .form-control {
    border-color: #667eea;
    font-size: 14px;
}

.pagination-jump .btn {
    border-color: #667eea;
    background: #667eea;
    color: white;
}

.pagination-jump .btn:hover {
    background: #5a6fd8;
    border-color: #5a6fd8;
}

.pagination-sm > li > a,
.pagination-sm > li > span {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
    background-color: #667eea;
    border-color: #667eea;
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
    color: #999;
    cursor: not-allowed;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .results-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .result-card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .candidate-info {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .candidate-meta, .candidate-education {
        justify-content: center;
        flex-wrap: wrap;
    }

    .search-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .search-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .recruitment-search-form {
        padding: 1.5rem;
    }

    .search-form-header {
        padding: 1rem 1.5rem;
    }

    #matchDetailsModal .modal-dialog {
        width: 95%;
        margin: 10px auto;
    }

    #matchDetailsModal .col-md-6 {
        margin-bottom: 10px;
    }

    #matchDetailsModal .col-md-4 {
        margin-bottom: 15px;
    }

    .panel-footer .col-sm-4 {
        margin-bottom: 10px;
        text-align: center !important;
    }

    .pagination-jump {
        margin-top: 0;
    }

    .dataTables_paginate {
        text-align: center !important;
    }

    .pull-right {
        float: none !important;
    }
}

@media (max-width: 480px) {
    .results-grid {
        padding: 0.75rem;
    }

    .result-card-header {
        padding: 1rem;
    }

    .result-card-body {
        padding: 1rem;
    }

    .result-card-actions {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        justify-content: center;
        font-size: 1rem;
        padding: 0.75rem 1rem;
    }

    .candidate-name {
        font-size: 1.4rem;
    }

    .candidate-meta, .candidate-education {
        font-size: 0.9rem;
        gap: 0.5rem;
    }

    .search-title {
        font-size: 1.125rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .recruitment-search-form {
        padding: 1rem;
    }

    .search-form-header {
        padding: 1rem;
    }

    .pagination-sm > li > a,
    .pagination-sm > li > span {
        padding: 3px 6px;
        font-size: 11px;
    }

    .pagination-jump .input-group {
        width: 120px !important;
    }
}
</style>
