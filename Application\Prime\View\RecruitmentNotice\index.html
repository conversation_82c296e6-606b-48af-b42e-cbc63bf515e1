<include file="block/hat" />
<include file="RecruitmentNotice/common_styles" />

<div class="container-fluid">
    <div class="row">
        <include file="block/menu" />
        <div class="col-xs-12 col-sm-9 col-lg-10">
            <div class="recruitment-page-wrapper">
                <div class="recruitment-page-container">
                    <!-- 现代化页面标题 -->
                    <div class="recruitment-page-header">
                        <div class="recruitment-header-content">
                            <div class="recruitment-page-title">
                                <div class="recruitment-title-icon">
                                    <i class="fa fa-bullhorn"></i>
                                </div>
                                <div class="recruitment-title-text">
                                    <h1 class="recruitment-title-main">招聘公告管理</h1>
                                    <p class="recruitment-title-sub">Recruitment Notice Management</p>
                                </div>
                            </div>
                        </div>
                        <div class="recruitment-header-actions">
                            <a href="{:U('edit')}" class="recruitment-header-btn recruitment-header-btn-primary">
                                <i class="fa fa-plus"></i>
                                <span>添加公告</span>
                            </a>
                        </div>
                    </div>

                    <!-- 现代化搜索面板 -->
                    <div class="recruitment-search-container">
                        <div class="recruitment-search-header">
                            <h3 class="recruitment-search-title">
                                <div class="recruitment-title-icon" style="width: 2rem; height: 2rem; font-size: 1rem;">
                                    <i class="fa fa-search"></i>
                                </div>
                                筛选条件
                            </h3>
                        </div>
                        <div class="recruitment-search-body">
                            <form class="form-inline" method="get">
                                <div class="form-group">
                                    <label class="sr-only" for="keyword">搜索关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword"
                                           value="{$keyword}" placeholder="🔍 搜索标题或招聘单位">
                                </div>
                                <div class="form-group">
                                    <label class="sr-only" for="status">状态筛选</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">📋 全部状态</option>
                                        <volist name="statusOptions" id="option" key="k">
                                            <option value="{$k}" <if condition="$status eq $k">selected</if>>{$option.text}</option>
                                        </volist>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> 搜索
                                </button>
                                <a href="{:U('index')}" class="btn btn-default">
                                    <i class="fa fa-refresh"></i> 重置
                                </a>
                            </form>
                        </div>
                    </div>

                    <!-- 现代化卡片布局 -->
                    <div class="recruitment-cards-container">
                        <volist name="list" id="vo">
                            <div class="recruitment-card">
                                <!-- 卡片头部 -->
                                <div class="recruitment-card-header">
                                    <div class="recruitment-card-id">
                                        <i class="fa fa-file-text"></i> 公告 #{$vo.id}
                                    </div>
                                    <div class="recruitment-card-status">
                                        <if condition="$vo.status eq 1">
                                            <span class="status-badge status-active">
                                                <i class="fa fa-check"></i> 启用
                                            </span>
                                        <else />
                                            <span class="status-badge status-inactive">
                                                <i class="fa fa-times"></i> 停用
                                            </span>
                                        </if>
                                    </div>
                                </div>

                                <!-- 卡片主体 -->
                                <div class="recruitment-card-body">
                                    <!-- 标题和公司信息 -->
                                    <div class="recruitment-card-main-info">
                                        <div class="recruitment-card-title">
                                            <i class="fa fa-file-text"></i>
                                            <strong>{$vo.title}</strong>
                                        </div>
                                        <div class="recruitment-card-company">
                                            <i class="fa fa-building"></i>
                                            <span>{$vo.company_name}</span>
                                        </div>
                                        <div class="recruitment-card-time">
                                            <i class="fa fa-clock-o"></i>
                                            <span>{:date('Y-m-d H:i', $vo['create_time'])}</span>
                                        </div>
                                    </div>

                                    <!-- 描述信息 -->
                                    <if condition="!empty($vo.description)">
                                        <div class="recruitment-card-description">
                                            <i class="fa fa-info-circle"></i>
                                            <span>{$vo.description|mb_substr=0,100,'utf-8'}...</span>
                                        </div>
                                    </if>
                                </div>

                                <!-- 卡片底部操作按钮 -->
                                <div class="recruitment-card-actions">
                                    <a href="{:U('edit', ['id' => $vo['id']])}" class="recruitment-action-btn btn-edit">
                                        <i class="fa fa-edit"></i>
                                        <span>编辑</span>
                                    </a>
                                    <a href="{:U('posts', ['id' => $vo['id']])}" class="recruitment-action-btn btn-posts">
                                        <i class="fa fa-users"></i>
                                        <span>岗位</span>
                                    </a>
                                    <a href="{:U('executeMatch', ['notice_id' => $vo['id']])}" class="recruitment-action-btn btn-match" onclick="return confirm('确定要执行简历匹配吗？')">
                                        <i class="fa fa-cogs"></i>
                                        <span>匹配</span>
                                    </a>
                                    <a href="{:U('matchResults', ['notice_id' => $vo['id']])}" class="recruitment-action-btn btn-results">
                                        <i class="fa fa-list-alt"></i>
                                        <span>结果</span>
                                    </a>
                                    <a href="{:U('toggle', ['id' => $vo['id']])}" class="recruitment-action-btn btn-toggle" onclick="return confirm('确定要切换状态吗？')">
                                        <if condition="$vo.status eq 1">
                                            <i class="fa fa-pause"></i>
                                            <span>停用</span>
                                        <else />
                                            <i class="fa fa-play"></i>
                                            <span>启用</span>
                                        </if>
                                    </a>
                                    <a href="{:U('delete', ['id' => $vo['id']])}" class="recruitment-action-btn btn-delete" onclick="return confirm('确定要删除吗？删除后将清除所有相关数据！')">
                                        <i class="fa fa-trash"></i>
                                        <span>删除</span>
                                    </a>
                                </div>
                            </div>
                        </volist>

                        <!-- 空状态 -->
                        <if condition="empty($list)">
                            <div class="recruitment-empty-state">
                                <div class="empty-state-content">
                                    <i class="fa fa-inbox"></i>
                                    <h3>暂无招聘公告数据</h3>
                                    <p>还没有创建任何招聘公告，点击下方按钮开始添加</p>
                                    <a href="{:U('edit')}" class="btn btn-primary btn-lg">
                                        <i class="fa fa-plus"></i> 添加第一个公告
                                    </a>
                                </div>
                            </div>
                        </if>
                    </div>

                    <!-- 数据统计信息 -->
                    <div class="recruitment-stats-bar">
                        <div class="stats-info">
                            <i class="fa fa-info-circle"></i>
                            <span>共找到 <strong>{$count}</strong> 条招聘公告</span>
                            <if condition="$totalPages gt 1">
                                <span>，第 <strong>{$page}</strong> / <strong>{$totalPages}</strong> 页</span>
                            </if>
                        </div>
                    </div>

                    <!-- 现代化分页 -->
                    <if condition="$totalPages gt 1">
                        <div class="recruitment-pagination-container">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="dataTables_info">
                                        <i class="fa fa-info-circle"></i>
                                        共 <strong>{$count}</strong> 条记录，
                                        第 <strong>{$page}</strong> / <strong>{$totalPages}</strong> 页
                                    </div>
                                </div>
                                <div class="col-sm-4 text-center">
                                    <div class="pagination-jump">
                                        <div class="input-group input-group-sm" style="width: 150px; margin: 0 auto;">
                                            <span class="input-group-addon">跳转到</span>
                                            <input type="number" class="form-control text-center" id="jumpToPage"
                                                   min="1" max="{$totalPages}" value="{$page}">
                                            <span class="input-group-btn">
                                                <button class="btn btn-default" type="button" onclick="jumpToPage()">
                                                    <i class="fa fa-arrow-right"></i>
                                                </button>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="dataTables_paginate paging_simple_numbers pull-right">
                                        <ul class="pagination">
                                            <!-- 首页 -->
                                            <if condition="$page gt 1">
                                                <li>
                                                    <a href="{:U('index', array_merge($_GET, ['p' => 1]))}"
                                                       title="首页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-double-left"></i>
                                                    </a>
                                                </li>
                                            </if>

                                            <!-- 上一页 -->
                                            <if condition="$page gt 1">
                                                <li>
                                                    <a href="{:U('index', array_merge($_GET, ['p' => $page-1]))}"
                                                       title="上一页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-left"></i>
                                                    </a>
                                                </li>
                                            <else />
                                                <li class="disabled">
                                                    <span><i class="fa fa-angle-left"></i></span>
                                                </li>
                                            </if>

                                            <!-- 页码 -->
                                            <php>
                                                $start = max(1, $page - 2);
                                                $end = min($totalPages, $page + 2);

                                                if ($totalPages <= 7) {
                                                    $start = 1;
                                                    $end = $totalPages;
                                                }
                                            </php>

                                            <if condition="$start gt 1">
                                                <li class="disabled"><span>...</span></li>
                                            </if>

                                            <for start="start" end="end">
                                                <li <if condition="$i eq $page">class="active"</if>>
                                                    <a href="{:U('index', array_merge($_GET, ['p' => $i]))}">{$i}</a>
                                                </li>
                                            </for>

                                            <if condition="$end lt $totalPages">
                                                <li class="disabled"><span>...</span></li>
                                            </if>

                                            <!-- 下一页 -->
                                            <if condition="$page lt $totalPages">
                                                <li>
                                                    <a href="{:U('index', array_merge($_GET, ['p' => $page+1]))}"
                                                       title="下一页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-right"></i>
                                                    </a>
                                                </li>
                                            <else />
                                                <li class="disabled">
                                                    <span><i class="fa fa-angle-right"></i></span>
                                                </li>
                                            </if>

                                            <!-- 末页 -->
                                            <if condition="$page lt $totalPages">
                                                <li>
                                                    <a href="{:U('index', array_merge($_GET, ['p' => $totalPages]))}"
                                                       title="末页" data-toggle="tooltip">
                                                        <i class="fa fa-angle-double-right"></i>
                                                    </a>
                                                </li>
                                            </if>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </if>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>
</div>

<script>
// 跳转到指定页面
function jumpToPage() {
    var page = parseInt($('#jumpToPage').val());
    var totalPages = {$totalPages};

    if (isNaN(page) || page < 1 || page > totalPages) {
        alert('请输入有效的页码（1-' + totalPages + '）');
        $('#jumpToPage').focus();
        return;
    }

    var currentUrl = window.location.href;
    var url = new URL(currentUrl);
    url.searchParams.set('p', page);
    window.location.href = url.toString();
}

$(function() {
    // 初始化工具提示
    $('[data-toggle="tooltip"]').tooltip();

    // 跳转页面输入框回车事件
    $('#jumpToPage').on('keypress', function(event) {
        if (event.keyCode === 13) {
            jumpToPage();
        }
    });

    // 卡片点击效果
    $('.recruitment-card').on('click', function(e) {
        // 如果点击的是按钮，不触发卡片选中效果
        if (!$(e.target).closest('.recruitment-action-btn').length) {
            $(this).addClass('card-active').siblings().removeClass('card-active');
        }
    });

    // 搜索表单增强
    $('form').on('submit', function() {
        var keyword = $('input[name="keyword"]').val().trim();
        if (keyword === '') {
            $('input[name="keyword"]').removeAttr('name');
        }

        var status = $('select[name="status"]').val();
        if (status === '') {
            $('select[name="status"]').removeAttr('name');
        }
    });
});
</script>

<style>
/* 数据统计信息栏 */
.recruitment-stats-bar {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
}

.stats-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    color: #374151;
}

.stats-info i {
    color: #667eea;
    font-size: 1.25rem;
}

.stats-info strong {
    color: #667eea;
    font-weight: 600;
}

/* 招聘公告卡片样式 */
.recruitment-cards-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 0 0.5rem;
}

.recruitment-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.recruitment-card:hover {
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.recruitment-card.card-active {
    box-shadow: 0 8px 25px -5px rgba(102, 126, 234, 0.25);
    border-color: #667eea;
}

/* 卡片头部 */
.recruitment-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.75rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recruitment-card-id {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.recruitment-card-id i {
    font-size: 1.6rem;
}

.status-badge {
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-size: 1.15rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.status-active {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 卡片主体 */
.recruitment-card-body {
    padding: 2rem;
}

.recruitment-card-main-info {
    margin-bottom: 1.5rem;
}

.recruitment-card-title {
    font-size: 1.9rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    line-height: 1.4;
}

.recruitment-card-title i {
    color: #667eea;
    font-size: 1.6rem;
}

.recruitment-card-company,
.recruitment-card-time {
    font-size: 1.35rem;
    color: #718096;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.recruitment-card-company i,
.recruitment-card-time i {
    color: #a0aec0;
    width: 1.25rem;
    font-size: 1.25rem;
}

.recruitment-card-description {
    background: #f7fafc;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1.5rem;
    font-size: 1.25rem;
    color: #4a5568;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    line-height: 1.6;
}

.recruitment-card-description i {
    color: #667eea;
    margin-top: 0.25rem;
    flex-shrink: 0;
    font-size: 1.35rem;
}

/* 卡片操作按钮 */
.recruitment-card-actions {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: flex-end;
}

.recruitment-action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 1.15rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-height: 2.75rem;
}

.btn-edit {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
}

.btn-edit:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.4);
    color: white;
    text-decoration: none;
}

.btn-posts {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-posts:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.btn-match {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.btn-match:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.4);
    color: white;
    text-decoration: none;
}

.btn-results {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(6, 182, 212, 0.3);
}

.btn-results:hover {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(6, 182, 212, 0.4);
    color: white;
    text-decoration: none;
}

.btn-toggle {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
}

.btn-toggle:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.4);
    color: white;
    text-decoration: none;
}

.btn-delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.btn-delete:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
    color: white;
    text-decoration: none;
}

/* 空状态样式 */
.recruitment-empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.empty-state-content {
    text-align: center;
    padding: 4rem;
}

.empty-state-content i {
    font-size: 5rem;
    color: #e2e8f0;
    margin-bottom: 2rem;
}

.empty-state-content h3 {
    font-size: 2rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

.empty-state-content p {
    font-size: 1.25rem;
    color: #718096;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .recruitment-cards-container {
        gap: 1rem;
        padding: 0 0.25rem;
    }

    .recruitment-card-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .recruitment-action-btn {
        justify-content: center;
        width: 100%;
        font-size: 1.25rem;
        padding: 1rem 1.5rem;
        min-height: 3rem;
    }

    .recruitment-card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1.5rem;
    }

    .recruitment-card-title {
        font-size: 1.65rem;
    }

    .recruitment-card-company,
    .recruitment-card-time {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .recruitment-card-body {
        padding: 1.5rem;
    }

    .recruitment-card-header {
        padding: 1.25rem;
    }

    .recruitment-card-actions {
        padding: 1.25rem;
    }

    .recruitment-card-title {
        font-size: 1.375rem;
    }

    .recruitment-card-id {
        font-size: 1.25rem;
    }

    .recruitment-stats-bar {
        padding: 0.75rem 1rem;
        margin-bottom: 1rem;
    }

    .stats-info {
        font-size: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}
</style>
